import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";

export default function useWorkflows() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataWorkflows = ref<any>([]);
    const dataCounts = ref<any>([]);
    const workflowDetail = ref<any>([]);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const getAllWorkflows = async (
        page: number, 
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/workflows', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });
            
            if (response.data.status == 'success') {
                dataCounts.value          = response.data.counts;
                dataWorkflows.value       = response.data.workflows.data;
                paginate.links.prev       = response.data.workflows.links.prev;
                paginate.links.next       = response.data.workflows.links.next; 
                paginate.meta.currentPage = response.data.workflows.current_page;
                paginate.meta.perPage     = response.data.workflows.per_page;
                paginate.meta.from        = response.data.workflows.from;
                paginate.meta.to          = response.data.workflows.to;
                paginate.meta.total       = response.data.workflows.total;
                paginate.meta.lastPage    = response.data.workflows.last_page;
            }

            const query = {
                ...tab
            };

            await router.push({ name: 'WorkflowListData', query: { ...query, page, perPage,  } }).catch(()=>{});
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getAllWorkflows(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const showDetailWorkflow = async (workflowID: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.get(`/api/workflows/${workflowID}`); 
            console.log(response);
            if (response.data.status === true) {
                workflowDetail.value = response.data.data;
                return response.data.data;
            } else {
                $toast.open({
                    message: response.data.message || 'Không tìm thấy thông tin công việc',
                    type: "warning",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                return null;
            }
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    showDetailWorkflow(workflowID);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        } 
    }

    const storeWorkflow = async (formData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/process`, formData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeWorkflow(formData);
                }, 1000);

                return;
            }
            switch (error.response.status) {
                case 400:
                    if (error.response.data.error_code == 'KEY_DUPLICATE') {
                        $toast.open({
                            message: t('toast.error_code.KEY_DUPLICATE'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    if (error.response.data.error_code == 'INVALID_FORM_DATA') {
                        $toast.open({
                            message: t('toast.error_code.INVALID_FORM_DATA'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    if (error.response.data.error_code == 'INVALID_WORKFLOW_DATA') {
                        $toast.open({
                            message: t('toast.error_code.INVALID_WORKFLOW_DATA'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    if (error.response.data.error_code == 'INVALID_FIELD_DATA') {
                        $toast.open({
                            message: t('toast.error_code.INVALID_FIELD_DATA'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    if (error.response.data.error_code == 'INVALID_SETUP_PROCESS_DATA') {
                        $toast.open({
                            message: t('toast.error_code.INVALID_SETUP_PROCESS_DATA'),
                            type: "warning",
                            duration: 5000,
                            dismissible: true,
                            position: "bottom-right",
                        });
                    }
                    break;
                case 500:
                    $toast.open({
                        message: t('toast.error_code.SERVER_ERROR'),
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    break;
                default:
                    console.log(error);
                    break;
            }
        } finally {
            setIsLoading.value = false;
        }
    };

    const getFlowTransitions = async (workflowID: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.get(`/api/workflows/${workflowID}/flow-transitions`);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getFlowTransitions(workflowID);
                }, 1000);

                return;
            }
            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    return {
        setIsLoading,
        paginate,
        dataWorkflows,
        dataCounts,
        getAllWorkflows,
        storeWorkflow,
        getFlowTransitions
    }
}