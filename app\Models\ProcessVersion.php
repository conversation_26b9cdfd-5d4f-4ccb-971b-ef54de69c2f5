<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\ActiveGlobalScopeTrait;
use App\Enums\ProcessVersionStatus;

class ProcessVersion extends Model
{
    use ActiveGlobalScopeTrait, HasUuids;
    
    protected $table = 'process_version';
    protected $primaryKey = 'id';

    protected $fillable = [
        'process_id',
        'form_id',
        'scope_use',
        'followers',
        'process_manager',
        'job_manager',
        'version_number',
        'change_log',
        'is_active',
        'create_by',
    ];
    
    protected $casts = [
        'scope_use' => 'array',
        'followers' => 'array',
        'process_manager' => 'array',
        'job_manager' => 'array',
        'change_log' => 'array', 
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function scopeProcessVersionActive($query, $process_id)
    {
        return $query->where('process_id', $process_id)->where('is_active', ProcessVersionStatus::TRUE->value);
    }

    public function stageTransitions()
    {
        return $this->hasMany('App\Models\StageTransition', 'process_version_id', 'id');
    }

    public function stageEmailConfigs()
    {
        return $this->hasMany('App\Models\StageEmailConfig', 'process_version_id', 'id');
    }

    public function processTransitions()
    {
        return $this->hasMany('App\Models\ProcessTransition', 'process_version_id', 'id');
    }

    public function process()
    {
        return $this->belongsTo('App\Models\Process', 'process_id', 'id');
    }
    public function form()
    {
        return $this->belongsTo('App\Models\Form', 'form_id', 'id');
    }
}
