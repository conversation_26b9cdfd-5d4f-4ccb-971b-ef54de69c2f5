<template>
	<BAccordionItem :title="$t('job.workflow_info')" visible>
		<div class="table-responsive">
			<table class="table table-hover table-bordered workflow-table">
				<thead>
					<tr>
						<th class="align-middle">
							{{ $t('workflow.stage.from_stage') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.action.title') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.stage.to_stage') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.condition.title') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.email.title') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.sync_group.title') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.stage.back_to_stage') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.action.title_back') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.email.title_back') }}
						</th>
						<th class="align-middle">
							{{ $t('workflow.process_transition.title') }}
						</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(transition, index) in flowTransitions" :key="index">
						<!-- Giai đoạn bắt đầu -->
						<td @click="handleCellClick('from_stage', transition)" 
							:class="{ 'has-data': transition.from_stage?.name, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.from_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.from_stage?.name">
								<span>{{ transition.from_stage.name }}</span>
								<CBadge v-if="transition.from_stage.status" :color="getStatusBadge(transition.from_stage.status).color" class="ms-2">
									{{ getStatusBadge(transition.from_stage.status).text }}
								</CBadge>
							</div>
							<span v-else>{{ $t('common.no_data') }}</span>
						</td>
						
						<!-- Hành động chuyển tiếp -->
						<td @click="handleCellClick('action', transition)" 
							:class="{ 'has-data': transition.action?.name, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.action?.name ? $t('workflow.not_executable_tooltip') : ''">
							{{ transition.action?.name || $t('common.no_data') }}
						</td>
						
						<!-- Giai đoạn chuyển tiếp -->
						<td @click="handleCellClick('to_stage', transition)" 
							:class="{ 'has-data': transition.to_stage?.name, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.to_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.to_stage?.name">
								<span>{{ transition.to_stage.name }}</span>
								<CBadge v-if="transition.from_stage.status && transition.to_stage.id !== WORKFLOWS.STAGE.DONE && transition.to_stage.id !== WORKFLOWS.STAGE.FALSE" :color="getStatusBadge(transition.to_stage.status).color" class="ms-2">
									{{ getStatusBadge(transition.to_stage.status).text }}
								</CBadge>
							</div>
							<span v-else>{{ $t('common.no_data') }}</span>
						</td>
						
						<!-- Điều kiện chuyển tiếp -->
						<td @click="handleCellClick('conditions', transition)" 
							:class="{ 'has-data': transition.conditions && transition.conditions.length, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.conditions && transition.conditions.length ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.conditions && transition.conditions.length">
								<div v-for="(condition, cIdx) in transition.conditions" :key="`c-${cIdx}`">
									{{ condition.name }} ({{ condition.status ? $t('workflow.condition.status.success') : $t('workflow.condition.status.failure') }})
								</div>
							</div>
							<div v-else>{{ $t('common.no_data') }}</div>
						</td>
						
						<!-- Email gửi -->
						<td @click="handleCellClick('email_templates', transition)" 
							:class="{ 'has-data': transition.email_templates && transition.email_templates.length, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.email_templates && transition.email_templates.length ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.email_templates && transition.email_templates.length">
								<div v-for="(email, eIdx) in transition.email_templates" :key="`e-${eIdx}`">
									{{ email.name || email.subject || $t('common.no_data') }}
								</div>
							</div>
							<div v-else>{{ $t('common.no_data') }}</div>
						</td>
						
						<!-- Đồng bộ giai đoạn -->
						<td @click="handleCellClick('sync_groups', transition)" 
							:class="{ 'has-data': transition.sync_groups && transition.sync_groups.length, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.sync_groups && transition.sync_groups.length ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.sync_groups && transition.sync_groups.length">
								<div v-for="(syncGroup, sgIdx) in transition.sync_groups" :key="`sg-${sgIdx}`">
									{{ syncGroup.name || $t('common.no_data') }}
								</div>
							</div>
							<div v-else>{{ $t('common.no_data') }}</div>
						</td>
						
						<!-- Giai đoạn quay lại -->
						<td @click="handleCellClick('back_to_stage', transition)" 
							:class="{ 'has-data': transition.back_to_stage?.name, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.back_to_stage?.name ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.back_to_stage?.name">
								<span>{{ transition.back_to_stage.name }}</span>
							</div>
							<span v-else>{{ $t('common.no_data') }}</span>
						</td>
						
						<!-- Hành động quay lại -->
						<td @click="handleCellClick('action_back_to', transition)" 
							:class="{ 'has-data': transition.action_back_to?.name, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.action_back_to?.name ? $t('workflow.not_executable_tooltip') : ''">
							{{ transition.action_back_to?.name || $t('common.no_data') }}
						</td>
						
						<!-- Email quay lại -->
						<td @click="handleCellClick('email_template_back', transition)" 
							:class="{ 'has-data': transition.email_template_back && transition.email_template_back.length, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.email_template_back && transition.email_template_back.length ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.email_template_back && transition.email_template_back.length">
								<div v-for="(email, eIdx) in transition.email_template_back" :key="`e-${eIdx}`">
									{{ email.name || email.subject || $t('common.no_data') }}
								</div>
							</div>
							<div v-else>{{ $t('common.no_data') }}</div>
						</td>
						
						<!-- Công việc xử lý tiếp -->
						<td @click="handleCellClick('process_transition', transition)" 
							:class="{ 'has-data': transition.process_transition && transition.process_transition.length, 'not-executable': !transition.can_execute }"
							:title="!transition.can_execute && transition.process_transition && transition.process_transition.length ? $t('workflow.not_executable_tooltip') : ''">
							<div v-if="transition.process_transition && transition.process_transition.length">
								<div v-for="(pt, ptIdx) in transition.process_transition" :key="`pt-${ptIdx}`">
									{{ pt.name || $t('common.no_data') }}
								</div>
							</div>
							<div v-else>{{ $t('common.no_data') }}</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</BAccordionItem>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { useI18n } from "vue-i18n";
import { WORKFLOWS, PROCESS_STAGE_STATUS } from '@/constants/constants';

export default defineComponent({
    name: "WorkflowTransitionsTable",

    props: {
        flowTransitions: {
            type: Array as PropType<any[]>,
            default: () => []
        },
        enableClick: {
            type: Boolean,
            default: true
        }
    },

    emits: ['show-detail'],

    setup(props, { emit }) {
        const { t } = useI18n();

        const handleCellClick = (type: string, transition: any) => {
			console.log(props.enableClick);
			
            if (!props.enableClick) return;
            
            // For JobDetail.vue, check can_execute except for action
            if (type !== 'action' && !transition.can_execute) return;
            
            emit('show-detail', type, transition);
        };

        const getStatusBadge = (status: string) => {
            if (!status) {
                return { text: t('workflow.stage.status.pending'), color: 'primary' };
            }
            const statusKey = `workflow.stage.status.${status.toLowerCase()}`;

            const statusText = t(statusKey, status);

            let color = 'secondary';
            switch (status.toLowerCase()) {
                case PROCESS_STAGE_STATUS.PROCESSING:
                    color = 'warning';
                    break;
                case PROCESS_STAGE_STATUS.COMPLETED:
                    color = 'success';
                    break;
                case PROCESS_STAGE_STATUS.PENDING:
                    color = 'primary';
                    break;
                case PROCESS_STAGE_STATUS.CANCEL:
                    color = 'danger';
                    break;
            }

            return { text: statusText, color };
        };

        return {
            handleCellClick,
            getStatusBadge,
            WORKFLOWS,
        };
    },
});
</script>

<style scoped>
.workflow-table {
	border-collapse: collapse;
	width: 100%;
	font-size: 0.9rem;
}

.workflow-table th {
	background-color: #f8f9fa;
	vertical-align: middle;
	padding: 10px;
	white-space: nowrap;
}

.workflow-table td {
	padding: 8px;
	vertical-align: top;
	min-width: 200px;
}

.workflow-table td.has-data {
	cursor: pointer;
	position: relative;
}

.workflow-table td.has-data:hover {
	background-color: #e9f5ff;
}

.workflow-table td.has-data::after {
	content: "";
	position: absolute;
	bottom: 3px;
	right: 3px;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background-color: #0d6efd;
}

.workflow-table td.not-executable {
	cursor: not-allowed;
	opacity: 0.6;
	background-color: #f5f5f5;
}

.workflow-table td.not-executable:hover {
	background-color: #f5f5f5 !important;
}

.workflow-table td.not-executable.has-data::after {
	background-color: #dc3545;
}

.workflow-table tr:hover {
	background-color: #f5f5f5;
}

.workflow-table tr:nth-child(even) {
	background-color: #fafafa;
}

.table-responsive {
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}
</style>
