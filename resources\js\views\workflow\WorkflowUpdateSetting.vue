<template>
    <div>
        <h4>{{ $t('workflow.update_setting.title') }}</h4>
        <WorkflowTransitionsTable
            :flowTransitions="state.flowTransitions"
            :showDetailModal="false"
            @show-detail="handleShowDetail"
        />
        <loading :isLoading="setIsLoading" />
    </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue';
import Loading from '@/views/loading/Loading.vue'
import WorkflowTransitionsTable from '@/components/WorkflowTransitionsTable.vue'
import useWorkflow from '@/composables/workflow';
import { useRoute } from 'vue-router';

export default defineComponent({
    name: 'WorkflowUpdateSetting',

    components: {
        Loading,
        WorkflowTransitionsTable,
    },

    setup() {
        const route = useRoute();

        const state = reactive({
            flowTransitions: [] as any,
		});

        const { setIsLoading, getFlowTransitions } = useWorkflow();

        onMounted( async () => {
            const workflowId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
            let result = await getFlowTransitions(workflowId);
            console.log(result);
            if (result.status === 'success') {
                state.flowTransitions = result.data_flow_transitions.flow_transitions;
            }
        });

        const handleShowDetail = (type: string, data: any) => {
            // Handle show detail logic for WorkflowUpdateSetting
            console.log('Show detail:', type, data);
        };

        return {
            setIsLoading,
            state,
            handleShowDetail,
        };
    },
});
</script>
