<?php
namespace App\Repositories\Process;

use App\Repositories\EloquentRepository;
use App\Enums\ProcessStatus;
use App\Repositories\User\UserRepositoryInterface;

class ProcessRepository extends EloquentRepository implements ProcessRepositoryInterface
{
	private $userRepository;

	public function __construct(UserRepositoryInterface $userRepository)
	{
		parent::__construct();
		$this->userRepository = $userRepository;
	}

	public function getModel()
	{
		return \App\Models\Process::class;
	}

	public function getWorkflowByScopeUses($search)
	{
		$select_columns = [
			'id',
			'name',
			'status',
		];

		$workflows = $this->model
			->select($select_columns)
			->with(['processVersionActive:id,process_id,form_id,scope_use,job_manager,followers'])
			->when(($search), function($query) use ($search) {
				$query->where('name', 'LIKE', '%' . $search . '%');
			})
			->where('status', ProcessStatus::ACTIVE->value)
			->limit(20)
			->get();

		// Xử lý where với cột scope_use để những user nào có quyền mới được sử dụng
		$userId = auth()->id(); // Lấy ID của user đang đăng nhập
        $workflows = $workflows->filter(function($workflow) use ($userId) {
            $scopeUses = $workflow->processVersionActive->scope_use;
            if (is_null($scopeUses)) {
                return true; // Nếu scope_use là null, mặc định được lấy
            }
            $usersInScope = $this->userRepository->getUserByOptionScopeRes($scopeUses, $workflow->id);
            return in_array($userId, array_column($usersInScope, 'value'));
        });
		// dd($workflows);
		return $workflows;	
	}

	public function getAllWorkflowRes($dataSearch)
	{
		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab']) 
			? ProcessStatus::fromString($dataSearch['tab']) 
			: ProcessStatus::ALL;
		
		$page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;

		$select_columns = [
			'id',
			'name',
			'description',
			'process_group_id',
			'status',
			'create_by',
			'created_at',
		];
		
		$query = $this->model
			->with(['processGroup:id,name', 'createdBy:id,full_name'])
			->select($select_columns);

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;
        // Áp dụng điều kiện theo tab
        $tabStatus->applyToQuery($query);
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        // Lấy danh sách trạng thái từ enum
        $statusList = ProcessStatus::cases();
        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $status->applyToQuery($countQuery);
            $counts[strtolower($status->name)] = $countQuery->count();
        }

        if ($page && $perPage) {
            $workflows = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $workflows = $query->get();
        }
        
        return [
            'workflows' => $workflows,
            'counts' => $counts
        ];
	}
}
?>