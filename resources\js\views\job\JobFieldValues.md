# JobFieldValues Component

## Mô tả
Component `JobFieldValues` được tách ra từ `JobDetail.vue` để hiển thị các field values của job một cách có thể tái sử dụng. Component này xử lý việc hiển thị các loại field khác nhau bao gồm:

- **FILEUPLOAD**: Hiển thị danh sách file với link download
- **TABLE**: Hiển thị bảng với các cột động, hỗ trợ OBJECTSYSTEM và FILEUPLOAD trong cell
- **OBJECTSYSTEM**: Hiển thị object với thuộc tính main và sub
- **Các loại khác**: Hiển thị giá trị text đơn giản

## Props

### fieldValues
- **Type**: `FieldValue[]`
- **Required**: `true`
- **Default**: `[]`
- **Description**: Mảng các field values cần hiển thị

```typescript
interface FieldValue {
    id: number;
    label: string;
    field?: {
        type: string;
    };
    extracted_values?: any[];
    field_value?: any;
    table_values?: any[];
    table_columns_type?: Record<string, string>;
}
```

## Events

### file-click
- **Payload**: `string` - Đường dẫn file được click
- **Description**: Được emit khi user click vào một file

## Sử dụng

### Cơ bản
```vue
<template>
    <CForm class="row g-2">
        <JobFieldValues 
            :field-values="jobDetail?.job_field_values || []"
            @file-click="handleFileClick"
        />
    </CForm>
</template>

<script>
import JobFieldValues from '@/views/job/JobFieldValues.vue'

export default {
    components: {
        JobFieldValues
    },
    
    setup() {
        const handleFileClick = (file) => {
            // Xử lý click file
            console.log('File clicked:', file);
        };
        
        return {
            handleFileClick
        };
    }
}
</script>
```

### Với data mẫu
```javascript
const testFieldValues = [
    {
        id: 1,
        label: 'Text Field',
        field: { type: 'TEXT' },
        field_value: 'Sample text value'
    },
    {
        id: 2,
        label: 'File Upload Field',
        field: { type: 'FILEUPLOAD' },
        field_value: ['file1.pdf', 'file2.jpg']
    },
    {
        id: 3,
        label: 'Table Field',
        field: { type: 'TABLE' },
        table_values: [
            { 'Column 1': 'Value 1', 'Files': ['file.pdf'] }
        ],
        table_columns_type: {
            'Column 1': 'TEXT',
            'Files': 'FILEUPLOAD'
        }
    }
];
```

## Dependencies

### Composables
- `useJobFieldValues`: Logic xử lý field values
- `useFiles`: Utilities cho file handling

### Components
- `CCol`, `CFormLabel`, `CTable`: CoreUI components

## Tính năng

### 1. File Upload Fields
- Hiển thị danh sách file
- Click để xem/download file
- Hỗ trợ multiple files

### 2. Table Fields
- Hiển thị bảng với header động
- Hỗ trợ OBJECTSYSTEM columns
- Hỗ trợ FILEUPLOAD trong cells
- Responsive design

### 3. Object System Fields
- Hiển thị main value
- Hỗ trợ sub properties
- Format multiple objects

### 4. Type Safety
- TypeScript interfaces
- Proper type checking
- Optional chaining cho safety

## Testing

Sử dụng `JobFieldValuesTest.vue` để test component với data mẫu:

```bash
# Import component test vào router hoặc sử dụng trực tiếp
import JobFieldValuesTest from '@/views/job/JobFieldValuesTest.vue'
```

## Best Practices

1. **Luôn truyền array rỗng làm fallback**: `fieldValues || []`
2. **Handle file click event**: Implement logic xử lý file click phù hợp
3. **Type safety**: Sử dụng TypeScript interfaces
4. **Error handling**: Component tự handle các trường hợp undefined/null

## Migration từ JobDetail.vue

Thay thế:
```vue
<!-- Cũ -->
<template v-for="fieldValue in jobDetail?.job_field_values" :key="fieldValue.id">
    <!-- Complex template logic -->
</template>

<!-- Mới -->
<JobFieldValues 
    :field-values="jobDetail?.job_field_values || []"
    @file-click="handleFileClick"
/>
```

## Lợi ích

1. **Tái sử dụng**: Có thể dùng ở nhiều component khác
2. **Maintainability**: Logic tập trung, dễ maintain
3. **Type Safety**: TypeScript support đầy đủ
4. **Performance**: Tối ưu rendering
5. **Testing**: Dễ test riêng biệt
