<template>
    <BModal
        centered
        scrollable
        no-close-on-backdrop
        no-close-on-esc
        size="lg"
        :title="$t('user.update_role')"
        v-model="isVisible"
        @hidden="handleClose"
    >
        <div v-if="setIsLoading" class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">{{ $t('common.loading') }}</span>
            </div>
        </div>

        <div v-else-if="workflow">
            <CRow class="mb-2">
                <CCol :xs="4">
                    <label class="mb-1">
                        {{ $t('workflow.name') }}
                        <span class="text-danger">*</span>
                    </label>
                    <Field 
                        v-model="workflow.name" 
                        name="workflow_name"
                        type="text" 
                        class="form-control" 
                        :placeholder="$t('workflow.name')"
                        maxlength="200" 
                    />
                    <ErrorMessage
                        as="div"
                        name="workflow_name"
                        class="text-danger"
                    />
                </CCol>
                <CCol :xs="4">
                    <div class="d-flex align-items-center">
                        <label class="mb-1">
                            {{ $t('workflow.process_group') }} 
                        </label>
                        <span 
                            @click="createProcessGroup()" 
                            class="material-symbols-outlined ms-1 cursor-pointer"
                            v-b-tooltip.hover
                            :title="$t('process_group.create')"
                        >
                            add_circle
                        </span>
                        <BModal 
                            size="md" 
                            hide-footer
                            no-close-on-backdrop
                            no-close-on-esc
                            centered 
                            :title="$t('process_group.create')"
                            v-model="state.showModalProcessGroup"
                            @hidden="resetModalProcessGroup"
                            @show="resetModalProcessGroup"
                        >
                            <div v-if="state.showComponentModal">
                                <process-group-add
                                    :dataProcessGroup="state.dataProcessGroup"
                                    @close-modal-process-group="hideModalProcessGroup"
                                >
                                </process-group-add>
                            </div>
                        </BModal>
                    </div>
                    <Multiselect
                        v-model="workflow.process_group" 
                        :placeholder="$t('workflow.choose')"
                        :close-on-select="false"
                        :filter-results="false"
                        :resolve-on-load="false"
                        :infinite="true"
                        :limit="10"
                        :clear-on-search="true"
                        :searchable="true"
                        :delay="0"
                        :min-chars="0"
                        :object="true"
                        :options="async (query) => {
                            return await debouncedGetOptionProcessGroups(query)
                        }"
                        @open="debouncedGetOptionProcessGroups('')"
                        :can-clear="false"
                    />
                </CCol>
            </CRow>
            <CRow class="mb-2">
                <CCol :xs="8">
                    <label class="mb-1">
                        {{ $t('workflow.description') }}
                    </label>
                    <Field 
                        v-model="workflow.description" 
                        name="description"
                        as="textarea"
                        class="form-control" 
                        :placeholder="$t('workflow.description')"
                        maxlength="500" 
                        rows="2"
                    />
                </CCol>
            </CRow>
        </div>

        <div v-else class="text-center py-4">
            <div class="text-muted">
                <span class="material-symbols-outlined fs-1 d-block mb-2">error</span>
                {{ $t('user.failed_to_load') }}
            </div>
        </div>

        <template #footer>
            <div class="d-flex justify-content-end w-100">
                <CButton color="secondary" @click="handleClose" class="me-2">
                    {{ $t('common.close') }}
                </CButton>
                <CButton
                    color="primary"
                    @click="saveWorkflow"
                    :disabled="setIsLoading || !hasChanges"
                >
                    <span v-if="setIsLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                    {{ $t('common.save') }}
                </CButton>
            </div>
        </template>
    </BModal>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import useWorkflow from '@/composables/workflow'
import { useToast } from 'vue-toast-notification'
import moment from 'moment'

export default defineComponent({
    name: 'UserRole',

    props: {
        visible: {
            type: Boolean,
            default: false
        },
        workflowData: {
            type: Object as () => any,
            default: null
        }
    },

    emits: ['close', 'updated'],

    setup(props, { emit }) {
        const { t } = useI18n()
        const { setIsLoading, updateWorkflow } = useWorkflow()
        const $toast = useToast()

        const isVisible = ref(props.visible)
        const workflow = ref(props.workflowData)

        // Check if there are changes
        const hasChanges = computed(() => {
            return JSON.stringify(workflow.value) !== JSON.stringify(props.workflowData);
        })

        const loadAllRoles = async () => {
            try {
                await getAllRoleOptions();
            } catch (error) {
                console.error('Error loading roles:', error)
            }
        }

        const initializeData = () => {
            if (props.workflowData) {
                workflow.value = props.workflowData 
            }
        }

        const saveWorkflow = async () => {
            if (!workflow.value || !hasChanges.value) return;
            const result = await updateWorkflow(workflow.value.id, workflow.value);
            if (result && result.status === 'success') {
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                })

                // Update original role IDs
                originalRoleIds.value = [...roleIds];

                // Emit updated event with the updated user data from API response
                emit('updated');
                handleClose();
            }
        }

        const handleClose = () => {
            isVisible.value = false;
            emit('close');
        }

        // Watch for visibility changes
        watch(() => props.visible, (newVal) => {
            isVisible.value = newVal;
            if (newVal) {
                initializeData();
                loadAllRoles();
            }
        })

        // Watch for userData changes
        watch(() => props.userData, (newVal) => {
            if (newVal) {
                initializeData();
            }
        })

        return {
            isVisible,
            setIsLoading,
            workflow,
            hasChanges,
            saveWorkflow,
            handleClose
        }
    }
})
</script>

<style scoped>
.user-info-section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

.role-item {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.role-item:hover {
    background-color: #e9ecef;
}

.roles-list {
    max-height: 300px;
    overflow-y: auto;
}
</style>