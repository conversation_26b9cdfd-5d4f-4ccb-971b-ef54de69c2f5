<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-4">
				<Form ref="form" @submit="handleSubmitWorkflow" :validation-schema="schema('WORKFLOW')">
					<BAccordion flush free>
						<BAccordionItem :title="$t('workflow.tab.general_infor')" visible>
							<CRow class="mb-2">
								<CCol :xs="4">
									<label class="mb-1">
										{{ $t('workflow.name') }}
										<span class="text-danger">*</span>
									</label>
									<Field 
										v-model="state.dataWorkflow.name" 
										name="workflow_name"
										type="text" 
										class="form-control" 
										:placeholder="$t('workflow.name')"
										maxlength="200" 
									/>
									<ErrorMessage
										as="div"
										name="workflow_name"
										class="text-danger"
									/>
								</CCol>
								<CCol :xs="4">
									<div class="d-flex align-items-center">
										<label class="mb-1">
											{{ $t('workflow.process_group') }} 
										</label>
										<span 
											@click="createProcessGroup()" 
											class="material-symbols-outlined ms-1 cursor-pointer"
											v-b-tooltip.hover
											:title="$t('process_group.create')"
										>
											add_circle
										</span>
										<BModal 
											size="md" 
											hide-footer
											no-close-on-backdrop
											no-close-on-esc
											centered 
											:title="$t('process_group.create')"
											v-model="state.showModalProcessGroup"
											@hidden="resetModalProcessGroup"
											@show="resetModalProcessGroup"
										>
											<div v-if="state.showComponentModal">
												<process-group-add
													:dataProcessGroup="state.dataProcessGroup"
													@close-modal-process-group="hideModalProcessGroup"
												>
												</process-group-add>
											</div>
										</BModal>
									</div>
									<Multiselect
										v-model="state.dataWorkflow.process_group" 
										:placeholder="$t('workflow.choose')"
										:close-on-select="false"
										:filter-results="false"
										:resolve-on-load="false"
										:infinite="true"
										:limit="10"
										:clear-on-search="true"
										:searchable="true"
										:delay="0"
										:min-chars="0"
										:object="true"
										:options="async (query) => {
											return await debouncedGetOptionProcessGroups(query)
										}"
										@open="debouncedGetOptionProcessGroups('')"
										:can-clear="false"
									/>
								</CCol>
							</CRow>
							<CRow class="mb-2">
								<CCol :xs="8">
									<label class="mb-1">
										{{ $t('workflow.description') }}
									</label>
									<Field 
										v-model="state.dataWorkflow.description" 
										name="description"
										as="textarea"
										class="form-control" 
										:placeholder="$t('workflow.description')"
										maxlength="500" 
										rows="2"
									/>
								</CCol>
							</CRow>
						</BAccordionItem>
						<BAccordionItem :title="$t('workflow.tab.permission')" visible>
							<CRow class="mb-2">
								<CCol :xs="4">
									<div class="d-flex align-items-center">
										<label class="mb-1">
											{{ $t('workflow.scope_use') }}
										</label>
										<span 
											class="material-symbols-outlined ms-1 cursor-pointer icon-info"
											v-b-tooltip.hover
											:title="$t('workflow.scope_use_desc')"
										>
											info
										</span>
									</div>
									<Multiselect
										mode="tags"
										v-model="state.dataWorkflowVersion.scope_use" 
										:placeholder="$t('workflow.choose')"
										:close-on-select="false"
										:filter-results="false"
										:resolve-on-load="false"
										:infinite="true"
										:limit="20"
										:clear-on-search="true"
										:searchable="true"
										:delay="0"
										:min-chars="0"
										:object="true"
										:options="async (query) => {
											return await debouncedGetOptionScopes(query)
										}"
										@open="debouncedGetOptionScopes('')"
										:can-clear="false"
									>
										<template v-slot:option="{ option }">
											<div class="custom-option">
												<div class="option-label mb-1">
													{{ option.label }}
												</div>
												<div class="option-description text-secondary">
													<small>
														<i>{{ option.description }}</i>
													</small>
												</div>
											</div>
										</template>
									</Multiselect>
								</CCol>
								<CCol :xs="4">
									<label class="mb-1">
										{{ $t('workflow.followers') }}
									</label>
									<Multiselect
										mode="tags"
										v-model="state.dataWorkflowVersion.followers" 
										:placeholder="$t('workflow.choose')"
										:close-on-select="false"
										:filter-results="false"
										:resolve-on-load="false"
										:infinite="true"
										:limit="20"
										:clear-on-search="true"
										:searchable="true"
										:delay="0"
										:min-chars="0"
										:object="true"
										:options="async (query) => {
											return await debouncedGetOptionScopes(query)
										}"
										@open="debouncedGetOptionScopes('')"
										:can-clear="false"
									>
										<template v-slot:option="{ option }">
											<div class="custom-option">
												<div class="option-label mb-1">
													{{ option.label }}
												</div>
												<div class="option-description text-secondary">
													<small>
														<i>{{ option.description }}</i>
													</small>
												</div>
											</div>
										</template>
									</Multiselect>
								</CCol>
							</CRow>
							<CRow class="mb-2">
								<CCol :xs="4">
									<div class="d-flex align-items-center">
										<label class="mb-1">
											{{ $t('workflow.process_manager') }}
											<span class="text-danger">*</span>
										</label>
										<span 
											class="material-symbols-outlined ms-1 cursor-pointer icon-info"
											v-b-tooltip.hover
											:title="$t('workflow.process_manager_desc')"
										>
											info
										</span>
									</div>
									<Field 
										name="process_manager"
										v-slot="{ field }"
									>
										<Multiselect
											mode="tags"
											v-bind="field"
											v-model="state.dataWorkflowVersion.process_manager" 
											:placeholder="$t('workflow.choose')"
											:close-on-select="false"
											:filter-results="false"
											:resolve-on-load="false"
											:infinite="true"
											:limit="20"
											:clear-on-search="true"
											:searchable="true"
											:delay="0"
											:min-chars="0"
											:object="true"
											:options="async (query) => {
												return await debouncedGetOptionScopes(query)
											}"
											@open="debouncedGetOptionScopes('')"
											:can-clear="false"
										>
											<template v-slot:option="{ option }">
												<div class="custom-option">
													<div class="option-label mb-1">
														{{ option.label }}
													</div>
													<div class="option-description text-secondary">
														<small>
															<i>{{ option.description }}</i>
														</small>
													</div>
												</div>
											</template>
										</Multiselect>
									</Field>
									<ErrorMessage
										as="div"
										name="process_manager"
										class="text-danger"
									/>
								</CCol>
								<CCol :xs="4">
									<div class="d-flex align-items-center">
										<label class="mb-1">
											{{ $t('workflow.job_manager') }}
											<span class="text-danger">*</span>
										</label>
										<span 
											class="material-symbols-outlined ms-1 cursor-pointer icon-info"
											v-b-tooltip.hover
											:title="$t('workflow.job_manager_desc')"
										>
											info
										</span>
									</div>
									<Field 
										name="job_manager"
										v-slot="{ field }"
									>
										<Multiselect
											mode="tags"
											v-bind="field"
											v-model="state.dataWorkflowVersion.job_manager" 
											:placeholder="$t('workflow.choose')"
											:close-on-select="false"
											:filter-results="false"
											:resolve-on-load="false"
											:infinite="true"
											:limit="20"
											:clear-on-search="true"
											:searchable="true"
											:delay="0"
											:min-chars="0"
											:object="true"
											:options="async (query) => {
												return await debouncedGetOptionScopes(query)
											}"
											@open="debouncedGetOptionScopes('')"
											:can-clear="false"
										>
											<template v-slot:option="{ option }">
												<div class="custom-option">
													<div class="option-label mb-1">
														{{ option.label }}
													</div>
													<div class="option-description text-secondary">
														<small>
															<i>{{ option.description }}</i>
														</small>
													</div>
												</div>
											</template>
										</Multiselect>
									</Field>
									<ErrorMessage
										as="div"
										name="job_manager"
										class="text-danger"
									/>
								</CCol>
							</CRow>
						</BAccordionItem>
						<BAccordionItem :title="$t('workflow.tab.form')" visible>
							<div class="row col-md-9">
								<div class="col-md-3">
									<div class="feature-card" @click="createForm()">
										<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><g><rect fill="none" height="24" width="24"/>
											<path d="M13,11H4c-1.1,0-2-0.9-2-2V6c0-1.1,0.9-2,2-2h9V11z M4,9h7V6H4V9z M15,20H4c-1.1,0-2-0.9-2-2v-3c0-1.1,0.9-2,2-2h11V20z M4,18h9v-3H4V18z M22,9h-2l2-5h-7v7h2v9L22,9z M4.75,17.25h1.5v-1.5h-1.5V17.25z M4.75,8.25h1.5v-1.5h-1.5V8.25z"/></g>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.create_form') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.create_form_desc') }}
											</p>
										</div>
									</div>
								</div>
							</div>
							<BModal 
								size="xl" 
								hide-footer
								no-close-on-backdrop
								no-close-on-esc
								scrollable 
								:title="$t('workflow.create_form')"
								v-model="state.showModalForm"
								class="modal-fullscreen"
							>
								<CRow class="mb-3">
									<CCol :xs="2">
										<div class="list-group">
											<div>
												<button
													v-for="(typeField, index) in state.typeFields"
													:key="index"
													type="button"
													class="list-group-item list-group-item-action d-flex align-items-center"
													@click.prevent="selectTypeField(typeField)"
												>
													<div class="d-flex align-items-center justify-content-center">
														<svg
															:xmlns="typeField.svg.xmlns"
															:height="typeField.svg.height"
															:viewBox="typeField.svg.viewBox"
															:width="typeField.svg.width"
															:fill="typeField.svg.fill"
														>
															<path v-for="(path, idx) in typeField.svg.paths" :key="idx" :d="path" />
														</svg>
													</div>
													<div class="ms-3">
														<small class="d-block text-uppercase">
															{{ $t(typeField.label) }}
														</small>
														<small class="d-block">
															{{ $t(typeField.desc) }}
														</small>
													</div>
												</button>
											</div>
										</div>
									</CCol>
									<CCol :xs="8">
										<FormKit
											ref="dynamicForm"
											type="form"
											v-model="state.dataFormWorkflow"
											:actions="false"
											incomplete-message=" "
											@submit="handleSubmitForm"
										>
											<CRow>
												<CCol :xs="4">
													<FormKit
														label-class="required-label" 
														type="text"
														:label="$t('form.name')"
														:floating-label="true"
														name="name"
														validation="required|length:1,200"
														:validation-messages="{
															required: `${$t('form.name')} ${$t('form.validate.required')}`,
															length: `${$t('form.name')} ${$t('form.validate.name_length')}`
														}"
													/>
												</CCol>
												<CCol :xs="8">
													<FormKit
														type="text"
														:label="$t('form.description')"
														:floating-label="true"
														name="description"
														validation="length:1,500"
														:validation-messages="{
															length: `${$t('form.description')} ${$t('form.validate.description_length')}`
														}"
													/>
												</CCol>
											</CRow>
										</FormKit>
										<CRow>
											<BTabs no-body content-class="mt-3" v-model="state.tabIndex">
												<BTab :title="$t('form.field.created')" @click="getFieldCreated()" disabled>
													<CTable align="middle" responsive>
														<table class="table table-hover">
															<thead>
																<tr>
																	<th>{{ $t('form.field.name') }}</th>
																	<th>{{ $t('form.field.keyword') }}</th>
																	<th>{{ $t('form.field.is_active') }}</th>
																	<th>{{ $t('form.field.type') }}</th>
																	<th>{{ $t('form.field.required') }}</th>
																	<th>{{ $t('form.field.stage_id') }}</th>
																	<th>{{ $t('form.field.create_by') }}</th>
																	<th>{{ $t('form.field.created_at') }}</th>
																</tr>
															</thead>
															<tbody>
																<tr 
																	v-for="(field, index) in state.fieldCreateds" 
																	:key="index"
																	@contextmenu="handleContextMenu($event, field, index)"
																	@touchstart="startPress($event, field, index)"
																	@touchend="endPress"
																>
																	<td class="align-middle">{{ field.display_name }}</td> 
																	<td class="align-middle">{{ field.keyword }}</td> 
																	<td class="align-middle">
																		<span class="badge bg-success" v-if="field.is_active === 1 || field.is_active === true">
																			{{ $t('form.field.active') }}
																		</span>
																		<span class="badge bg-danger" v-else>
																			{{ $t('form.field.unactive') }}
																		</span>
																	</td> 
																	<td class="align-middle">{{ field.type }}</td> 
																	<td class="align-middle">
																		{{ field.required === 1 || field.required === true ? $t('form.field.yes') : $t('form.field.no') }}
																	</td> 
																	<td class="align-middle">NULL</td> 
																	<td class="align-middle">NULL</td> 
																	<td class="align-middle">NULL</td> 
																</tr>
															</tbody>
														</table>
													</CTable>
												</BTab>
												<BTab :title="$t('form.field.setup')" @click="hiddenContextMenuVisible()">
													<CTable align="middle" responsive>
														<table class="table table-hover" v-if="state.fieldSetups.length > 0">
															<thead>
																<tr>
																	<th>{{ $t('form.field.name') }}</th>
																	<th>{{ $t('form.field.keyword') }}</th>
																	<th>{{ $t('form.field.is_active') }}</th>
																	<th>{{ $t('form.field.type') }}</th>
																	<th>{{ $t('form.field.required') }}</th>
																	<th>{{ $t('form.field.order') }}</th>
																	<th>{{ $t('form.field.stage_id') }}</th>
																	<th>{{ $t('form.field.column_width') }}</th>
																</tr>
															</thead>
															<tbody>
																<tr 
																	v-for="(field, index) in state.fieldSetups" 
																	:key="index"
																	@contextmenu="handleContextMenu($event, field, index)"
																	@touchstart="startPress($event, field, index)"
																	@touchend="endPress"
																>
																	<td class="align-middle">{{ field.display_name }}</td> 
																	<td class="align-middle">{{ field.keyword }}</td> 
																	<td class="align-middle">
																		<span class="badge bg-success">
																			{{ $t('form.field.setup') }}
																		</span>
																	</td> 
																	<td class="align-middle">{{ field.label_type }}</td> 
																	<td class="align-middle">
																		{{ field.required ? $t('form.field.yes') : $t('form.field.no') }}
																	</td> 
																	<td class="align-middle">{{ field.order }}</td> 
																	<td class="align-middle">{{ field.stages.name ? field.stages.name : $t('workflow.stage.option.start') }}</td> 
																	<td class="align-middle">
																		{{  $t(`options_width.${field.column_width}`) }}
																	</td> 
																</tr>
															</tbody>
														</table>
														<table class="table table-hover text-center" v-else>
															{{ $t('search.no_matching_records_found') }}
														</table>
													</CTable>
												</BTab>
											</BTabs>
										</CRow>
									</CCol>
									<CCol :xs="2" class="border-start">
										<FormKit 
											type="form" 
											v-model="state.dataFormField"
											:actions="false"
											#default="{ state: { valid } }"
											v-if="state.dataFields.length > 0"
											@submit="handleSubmitField(state.dataFormField['type'])"
											incomplete-message=" "
										>
											<Form @submit="handleSubmitField(state.dataFormField['type'])" :validation-schema="schema(state.dataFormField['type'])">
												<BTabs no-body>
													<div class="mb-3 mt-2">{{ $t('form.field.setting') }}</div>
													<BTab :title="$t('form.language.vi')" active>
														<div v-for="field in state.dataFields" :key="field.name" class="mb-2">
															<div v-if="field.type === 'addable'">
																<label class="mb-2">{{ $t('form.field.options') }}</label>
																<div v-if="state.dataFormField['type'] === 'SELECT'">
																	<div v-for="(option, index) in state.selectOptions" :key="index" class="d-flex flex-column mb-3">
																		<div class="d-flex align-items-center">
																			<Field 
																				v-model="state.selectOptions[index]" 
																				:name="`selectOptions[${index}]`"
																				type="text" 
																				class="form-control" 
																				maxlength="255" 
																			/>
																			<svg
																				xmlns="http://www.w3.org/2000/svg"
																				height="24px"
																				viewBox="0 -960 960 960"
																				width="24px"
																				fill="#83868C"
																				class="cursor-pointer ms-2"
																				@click="removeSelectOptions(index, state.dataFormField['type'])" 
																			>
																				<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
																			</svg>
																		</div>
																		<ErrorMessage
																			as="div"
																			:name="`selectOptions[${index}]`"
																			class="text-danger d-block mt-1"
																		/>
																	</div>
																</div>
																<div v-if="state.dataFormField['type'] === 'RADIO'">
																	<div v-for="(option, index) in state.selectOptionRadios" :key="index" class="d-flex flex-column mb-3">
																		<div class="d-flex align-items-center">
																			<Field 
																				v-model="state.selectOptionRadios[index]" 
																				:name="`selectOptionRadios[${index}]`"
																				type="text" 
																				class="form-control" 
																				maxlength="255" 
																			/>
																			<svg
																				xmlns="http://www.w3.org/2000/svg"
																				height="24px"
																				viewBox="0 -960 960 960"
																				width="24px"
																				fill="#83868C"
																				class="cursor-pointer ms-2"
																				@click="removeSelectOptions(index, state.dataFormField['type'])" 
																			>
																				<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
																			</svg>
																		</div>
																		<ErrorMessage
																			as="div"
																			:name="`selectOptionRadios[${index}]`"
																			class="text-danger d-block mt-1"
																		/>
																	</div>
																</div>
																<div v-if="state.dataFormField['type'] === 'CHECKLIST'">
																	<div v-for="(option, index) in state.selectOptionChecklists" :key="index" class="d-flex flex-column mb-3">
																		<div class="d-flex align-items-center">
																			<Field 
																				v-model="state.selectOptionChecklists[index]" 
																				:name="`selectOptionChecklists[${index}]`"
																				type="text" 
																				class="form-control" 
																				maxlength="255" 
																			/>
																			<svg
																				xmlns="http://www.w3.org/2000/svg"
																				height="24px"
																				viewBox="0 -960 960 960"
																				width="24px"
																				fill="#83868C"
																				class="cursor-pointer ms-2"
																				@click="removeSelectOptions(index, state.dataFormField['type'])" 
																			>
																				<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
																			</svg>
																		</div>
																		<ErrorMessage
																			as="div"
																			:name="`selectOptionChecklists[${index}]`"
																			class="text-danger d-block mt-1"
																		/>
																	</div>
																</div>
																<button 
																	type="button" 
																	class="btn btn-primary mt-2 d-flex align-items-center" 
																	@click="addItemSelectOptions(state.dataFormField['type'])"
																>
																	<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
																		<path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
																	</svg>
																</button>
															</div>
															<div v-else-if="field.type === 'multiselect'">
																<label class="mb-1">
																	{{ $t('form.field.default_value') }}
																</label>
																<div v-if="state.dataFormField['type'] === 'SELECT'">
																	<Multiselect
																		mode="tags"
																		v-model="state.defaultMultiSelectedOptions"
																		:placeholder="$t('form.select.select_multiple')"
																		:close-on-select="false"
																		:searchable="true"
																		:options="state.selectOptions"
																		v-if="state.dataFormField['multiple_value']"
																	/>
																	<Multiselect
																		v-model="state.defaultSelectedOptions"
																		:placeholder="$t('form.select.choose_one')"
																		:close-on-select="false"
																		:searchable="true"
																		:options="state.selectOptions"
																		v-else
																	/>
																</div>
																<div v-if="state.dataFormField['type'] === 'RADIO'">
																	<Multiselect
																		v-model="state.defaultSelectedOptions"
																		:placeholder="$t('form.select.choose_one')"
																		:close-on-select="false"
																		:searchable="true"
																		:options="state.selectOptionRadios"
																	/>
																</div>
																<div v-if="state.dataFormField['type'] === 'CHECKLIST'">
																	<Multiselect
																		mode="tags"
																		v-model="state.defaultMultiSelectedOptions"
																		:placeholder="$t('form.select.select_multiple')"
																		:close-on-select="false"
																		:searchable="true"
																		:options="state.selectOptionChecklists"
																	/>
																</div>
															</div>
															<div v-else-if="field.type === 'user'">
																<label class="mb-1">
																	{{ $t('form.field.default_value') }}
																</label>
																<Multiselect
																	mode="tags"
																	v-model="state.defaultMultiSelectedOptionUsers"
																	:placeholder="$t('form.select.select_multiple')"
																	:close-on-select="false"
																	:filter-results="false"
																	:resolve-on-load="false"
																	:infinite="true"
																	:limit="20"
																	:clear-on-search="true"
																	:searchable="true"
																	:delay="0"
																	:min-chars="0"
																	:object="true"
																	:options="async (query) => {
																		return await debouncedGetOptionUsers(query)
																	}"
																	@open="debouncedGetOptionUsers('')"
																	v-if="state.dataFormField['multiple_value']"
																/>
																<Multiselect
																	v-model="state.defaultSelectedOptionUsers"
																	:placeholder="$t('form.select.choose_one')"
																	:close-on-select="false"
																	:filter-results="false"
																	:resolve-on-load="false"
																	:infinite="true"
																	:limit="20"
																	:clear-on-search="true"
																	:searchable="true"
																	:delay="0"
																	:min-chars="0"
																	:object="true"
																	:options="async (query) => {
																		return await debouncedGetOptionUsers(query)
																	}"
																	@open="debouncedGetOptionUsers('')"
																	v-else
																/>
															</div>
															<div v-else-if="field.type === 'department'">
																<label class="mb-1">
																	{{ $t('form.field.default_value') }}
																</label>
																<Multiselect
																	mode="tags"
																	v-model="state.defaultMultiSelectedOptionDepartments"
																	:placeholder="$t('form.select.select_multiple')"
																	:close-on-select="false"
																	:searchable="true"
																	:object="true"
																	:options="state.selectOptionDepartments"
																	v-if="state.dataFormField['multiple_value']"
																>
																	<template v-slot:option="{ option }">
																		<div class="custom-option">
																			<div class="option-label mb-1">
																				{{ option.label }}
																			</div>
																			<div class="option-description text-secondary">
																				<small>
																					<i>{{ option.type }}</i>
																				</small>
																			</div>
																		</div>
																	</template>
																</Multiselect>
																<Multiselect
																	v-model="state.defaultSelectedOptionDepartments"
																	:placeholder="$t('form.select.choose_one')"
																	:close-on-select="false"
																	:searchable="true"
																	:object="true"
																	:options="state.selectOptionDepartments"
																	v-else
																>
																	<template v-slot:option="{ option }">
																		<div class="custom-option">
																			<div class="option-label mb-1">
																				{{ option.label }}
																			</div>
																			<div class="option-description text-secondary">
																				<small>
																					<i>{{ option.type }}</i>
																				</small>
																			</div>
																		</div>
																	</template>
																</Multiselect>
															</div>
															<div v-else-if="field.type === 'children'">
																<label class="mb-1">
																	{{ $t('form.field.children') }}
																	<span class="text-danger">*</span>
																</label>
																<Field 
																	name="validateMultiselectChildren"
																	v-slot="{ field }"
																>
																	<Multiselect
																		mode="tags"
																		v-bind="field"
																		v-model="state.defaultMultiSelectedOptions"
																		:placeholder="$t('form.select.select_multiple')"
																		:close-on-select="false"
																		:searchable="true"
																		:object="true"
																		:options="selectOptionChildrens"
																		:can-clear="false"
																		:can-deselect="false"
																	/>
																</Field>
																<ErrorMessage
																	as="div"
																	name="validateMultiselectChildren"
																	class="text-danger"
																/>
															</div>
															<div v-else-if="field.type === 'table'">
																<label class="mb-1">
																	{{ $t('form.field.table') }}
																	<span class="text-danger">*</span>
																</label>
																<Field 
																	name="validateMultiselectTable"
																	v-slot="{ field }"
																>
																	<Multiselect
																		v-bind="field"
																		v-model="state.defaultSelectedOptionTable"
																		:placeholder="$t('form.select.choose_one')"
																		:close-on-select="false"
																		:searchable="true"
																		:object="true"
																		:options="state.selectOptionTables"
																		@change="changeOptionTable($event)"
																		:can-clear="false"
																		:can-deselect="false"
																	/>
																</Field>
																<ErrorMessage
																	as="div"
																	name="validateMultiselectTable"
																	class="text-danger"
																/>
															</div>
															<div v-else-if="field.type === 'table_column'">
																<label class="mb-1">
																	{{ $t('form.field.table_column') }}
																	<span class="text-danger">*</span>
																</label>
																<Field 
																	name="validateMultiselectTableColumn"
																	v-slot="{ field }"
																>
																	<Multiselect
																		v-bind="field"
																		v-model="state.defaultSelectedOptionTableColumn"
																		:placeholder="$t('form.select.choose_one')"
																		:close-on-select="false"
																		:searchable="true"
																		:object="true"
																		:options="state.selectOptionTableColumns"
																		:can-clear="false"
																		:can-deselect="false"
																	/>
																</Field>
																<ErrorMessage
																	as="div"
																	name="validateMultiselectTableColumn"
																	class="text-danger"
																/>
															</div>
															<div v-else-if="field.type === 'table_column_sub'">
																<div v-show="state.dataFormField.multiple_value !== true && state.dataFormField.type === 'OBJECTSYSTEM'">
																	<label class="mb-1">
																		{{ $t('form.field.table_column_sub') }}
																	</label>
																	<Field 
																		name="validateMultiselectTableColumnSub"
																		v-slot="{ field }"
																	>
																		<Multiselect
																			mode="tags"
																			v-bind="field"
																			v-model="state.defaultSelectedOptionTableColumnSub"
																			:placeholder="$t('form.select.select_multiple')"
																			:close-on-select="false"
																			:searchable="true"
																			:options="state.selectOptionTableColumns"
																			:can-clear="false"
																			:can-deselect="false"
																		/>
																	</Field>
																</div>
															</div>
															<div v-else>
																<FormKit
																	v-bind="field"
																	v-model="state.dataFormField[field.name]"
																	@input="(value) => handleInputDisplayName(field.name, value, state.dataFormField.type)"
																/>
															</div>
														</div>
													</BTab>
													<BTab :title="$t('form.language.en')">
														<FormKit
															v-for="field in state.dataFieldEns"
															:key="field.name"
															v-bind="field"
															v-model="state.dataFormField[field.name]"
														/>
													</BTab>    
												</BTabs>
												<div class="d-flex justify-content-center" v-if="state.validateSubmitForm">
													<CButton 
														type="button"
														class="btn btn-light border m-2"
														@click="closeField"
													>
														<span class="text-uppercase">
															{{ $t('form.close') }}
														</span>
													</CButton>
													<CButton 
														type="submit"
														class="btn btn-primary m-2"
														:disabled="!valid"
													>
														<span class="text-uppercase">
															{{ $t('form.save_update') }}
														</span>
													</CButton>
												</div>
											</Form>
											<div class="d-flex justify-content-center" v-if="!state.validateSubmitForm">
												<CButton 
													type="button"
													class="btn btn-light border m-2"
													@click="closeField"
												>
													<span class="text-uppercase">
														{{ $t('form.close') }}
													</span>
												</CButton>
												<CButton 
													type="submit"
													class="btn btn-primary m-2"
												>
													<span class="text-uppercase">
														{{ $t('form.save_update') }}
													</span>
												</CButton>
											</div>
										</FormKit>
									</CCol>
								</CRow>	
								<CCardFooter>
									<div class="d-flex justify-content-start">
										<CButton 
											type="button"
											class="btn btn-primary m-1"
											@click.prevent="submitForm"
										>
											<span class="text-uppercase">
												{{ $t('workflow.save_form') }}
											</span>
										</CButton>
										<CButton 
											type="button"
											class="btn btn-light border m-1"
											@click="closeModalForm"
										>
											<span class="text-uppercase">
												{{ $t('workflow.close') }}
											</span>
										</CButton>
									</div>
								</CCardFooter>
							</BModal>
						</BAccordionItem>
						<BAccordionItem :title="$t('workflow.tab.process')" visible>
							<div class="row col-md-9">
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createStage()">
										<svg xmlns="http://www.w3.org/2000/svg" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><path d="M0 0h24v24H0V0z" fill="none"/>
											<path d="M14 6V4h-4v2h4zM4 8v11h16V8H4zm16-2c1.11 0 2 .89 2 2v11c0 1.11-.89 2-2 2H4c-1.11 0-2-.89-2-2l.01-11c0-1.11.88-2 1.99-2h4V4c0-1.11.89-2 2-2h4c1.11 0 2 .89 2 2v2h4z"/>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.stage_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.stage_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="lg" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable 
										:title="$t('workflow.feature.stage_title')"
										v-model="state.showModalStage"
										@hidden="resetModalStage"
										@show="resetModalStage"
									>
										<div v-if="state.showComponentModal">
											<stage-add
												:dataStage="state.dataStage"
												:listDataStages="state.listDataStages"
												@close-modal-stage="hideModalStage"
												@reset-modal-stage="resetModalStage"
												@add-stage="updateAddSelectOptionStages"
												@edit-stage="updateEditSelectOptionStages"
												@remove-stage="updateRemoveSelectOptionStages"
											>
											</stage-add>
										</div>
									</BModal>
								</div>
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createAction()">
										<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><g>
											<rect fill="none" height="24" width="24"/></g><g><g>
											<path d="M14.69,2.21L4.33,11.49c-0.64,0.58-0.28,1.65,0.58,1.73L13,14l-4.85,6.76c-0.22,0.31-0.19,0.74,0.08,1.01h0 c0.3,0.3,0.77,0.31,1.08,0.02l10.36-9.28c0.64-0.58,0.28-1.65-0.58-1.73L11,10l4.85-6.76c0.22-0.31,0.19-0.74-0.08-1.01l0,0 C15.47,1.93,15,1.92,14.69,2.21z"/></g></g>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.action_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.action_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="md" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable 
										:title="$t('workflow.feature.action_title')"
										v-model="state.showModalAction"
										@hidden="resetModalAction"
										@show="resetModalAction"
									>
										<div v-if="state.showComponentModal">
											<action-add
												:dataAction="state.dataAction"
												:listDataActions="state.listDataActions"
												@close-modal-action="hideModalAction"
												@reset-modal-action="resetModalAction"
												@add-action="updateAddSelectOptionActions"
												@edit-action="updateEditSelectOptionActions"
												@remove-action="updateRemoveSelectOptionActions"
											>
											</action-add>
										</div>
									</BModal>
								</div>
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createCondition()">
										<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C">
											<rect fill="none" height="24" width="24"/>
											<path d="M7.06 8.94 5 8l2.06-.94L8 5l.94 2.06L11 8l-2.06.94L8 11l-.94-2.06zM8 21l.94-2.06L11 18l-2.06-.94L8 15l-.94 2.06L5 18l2.06.94L8 21zm-3.63-8.63L3 13l1.37.63L5 15l.63-1.37L7 13l-1.37-.63L5 11l-.63 1.37zM12 12c0-3.09 1.38-5.94 3.44-8H12V2h7v7h-2V5.28c-1.8 1.74-3 4.2-3 6.72 0 3.32 2.1 6.36 5 7.82V22c-4.09-1.59-7-5.65-7-10zm12 2h-2v-2h-2v2h-2v2h2v2h2v-2h2v-2z"/>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.condition_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.condition_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="lg" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable 
										:title="$t('workflow.feature.condition_title')"
										v-model="state.showModalCondition"
										@hidden="resetModalCondition"
										@show="resetModalCondition"
									>
										<div v-if="state.showComponentModal">
											<condition-add
												:dataCondition="state.dataCondition"
												:listDataConditions="state.listDataConditions"
												:fieldSetups="state.fieldSetups"
												@close-modal-condition="hideModalCondition"
												@reset-modal-condition="resetModalCondition"
												@add-condition="updateAddSelectOptionConditions"
												@edit-condition="updateEditSelectOptionConditions"
												@remove-condition="updateRemoveSelectOptionConditions"
											>
											</condition-add>
										</div>
									</BModal>
								</div>
							</div>
							<div class="row col-md-9">
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createSyncGroup()">
										<svg xmlns="http://www.w3.org/2000/svg" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><path d="M0 0h24v24H0V0z" fill="none"/>
											<path d="M17 20.41L18.41 19 15 15.59 13.59 17 17 20.41zM7.5 8H11v5.59L5.59 19 7 20.41l6-6V8h3.5L12 3.5 7.5 8z"/>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.sync_group_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.sync_group_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="md" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable 
										:title="$t('workflow.feature.sync_group_title')"
										v-model="state.showModalSyncGroup"
										@hidden="resetModalSyncGroup"
										@show="resetModalSyncGroup"
									>
										<div v-if="state.showComponentModal">
											<sync-group-add
												:dataSyncGroup="state.dataSyncGroup"
												:listDataSyncGroups="state.listDataSyncGroups"
												@close-modal-sync-group="hideModalSyncGroup"
												@reset-modal-sync-group="resetModalSyncGroup"
												@add-sync-group="updateAddSelectOptionSyncGroups"
												@edit-sync-group="updateEditSelectOptionSyncGroups"
												@remove-sync-group="updateRemoveSelectOptionSyncGroups"
											>
											</sync-group-add>
										</div>
									</BModal>
								</div>
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createEmailTemplate()">
										<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><g>
											<rect fill="none" height="24" width="24"/>
											<path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h9v-2H4V8l8,5l8-5v5h2V6C22,4.9,21.1,4,20,4z M12,11L4,6h16L12,11z M19,15l4,4 l-4,4v-3h-4v-2h4V15z"/></g>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.email_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.email_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="lg" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable 
										:title="$t('workflow.feature.email_title')"
										v-model="state.showModalEmailTemplate"
										@hidden="resetModalEmailTemplate"
										@show="resetModalEmailTemplate"
									>
										<div v-if="state.showComponentModal">
											<email-template-add
												:dataEmailTemplate="state.dataEmailTemplate"
												:listDataEmailTemplates="state.listDataEmailTemplates"
												:fieldSetups="state.fieldSetups"
												:selectOptionConditions="state.selectOptionConditions"
												@close-modal-email-template="hideModalEmailTemplate"
												@reset-modal-email-template="resetModalEmailTemplate"
												@add-email-template="updateAddSelectOptionEmailTemplates"
												@edit-email-template="updateEditSelectOptionEmailTemplates"
												@remove-email-template="updateRemoveSelectOptionEmailTemplates"
											>
											</email-template-add>
										</div>
									</BModal>
								</div>
								<div class="col-md-3 mb-3">
									<div class="feature-card" @click="createProcessTransition()">
										<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="30px" viewBox="0 0 24 24" width="30px" fill="#83868C"><rect fill="none" height="24" width="24"/>
											<path d="M22,11V3h-7v3H9V3H2v8h7V8h2v10h4v3h7v-8h-7v3h-2V8h2v3H22z M7,9H4V5h3V9z M17,15h3v4h-3V15z M17,5h3v4h-3V5z"/>
										</svg>
										<div class="content-right">
											<p class="feature-title">
												{{ $t('workflow.feature.process_transition_title') }}
											</p>
											<p class="feature-desc">
												{{ $t('workflow.feature.process_transition_desc') }}
											</p>
										</div>
									</div>
									<BModal 
										size="lg" 
										hide-footer
										no-close-on-backdrop
										no-close-on-esc
										centered
										scrollable
										:title="$t('workflow.feature.process_transition_title')"
										v-model="state.showModalProcessTransition"
										@hidden="resetModalProcessTransition"
										@show="resetModalProcessTransition"
									>
										<div v-if="state.showComponentModal">
											<process-transition-add
												:dataProcessTransition="state.dataProcessTransition"
												:listDataProcessTransitions="state.listDataProcessTransitions"
												@close-modal-process-transition="hideModalProcessTransition"
												@reset-modal-process-transition="resetModalProcessTransition"
												@add-process-transition="updateAddSelectOptionProcessTransitions"
												@edit-process-transition="updateEditSelectOptionProcessTransitions"
												@remove-process-transition="updateRemoveSelectOptionProcessTransitions"
											>
											</process-transition-add>
										</div>
									</BModal>
								</div>
							</div>
							<CTable align="middle" responsive>
								<table class="table table-hover table-bordered">
									<thead class="text-center">
										<tr>
											<th class="align-middle">
												{{ $t('workflow.stage.from_stage') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.action.title') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.stage.to_stage') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.condition.title') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.email.title') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.sync_group.title') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.stage.back_to_stage') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.action.title_back') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.email.title_back') }}
											</th>
											<th class="align-middle">
												{{ $t('workflow.process_transition.title') }}
											</th>
											<th class="align-middle">
											</th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="(setupProcess, index) in state.dataSetupProcess" :key="index">
											<td class="align-middle table__td--from-stage">
												<Field 
													v-model="setupProcess.from_stage"
													:name="`workflows[${index}].from_stage`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionFromStage, indexOptionFromStage) in state.selectOptionFromStages" :value="optionFromStage.value" :key="indexOptionFromStage">
														{{ optionFromStage.label }}
													</option>
												</Field>
												<ErrorMessage
													as="div"
													:name="`workflows[${index}].from_stage`"
													class="text-danger"
												/>
											</td>
											<td class="align-middle table__td--action-next">
												<Field 
													v-model="setupProcess.action_next"
													:name="`workflows[${index}].action_next`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionActionNext, indexOptionAction) in state.selectOptionActionNexts" :value="optionActionNext.value" :key="indexOptionAction">
														{{ optionActionNext.label }}
													</option>
												</Field>
												<ErrorMessage
													as="div"
													:name="`workflows[${index}].action_next`"
													class="text-danger"
												/>
											</td>
											<td class="align-middle table__td--to-stage">
												<Field 
													v-model="setupProcess.to_stage"
													:name="`workflows[${index}].to_stage`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionToStage, indexOptionToStage) in state.selectOptionToStages" :value="optionToStage.value" :key="indexOptionToStage">
														{{ optionToStage.label }}
													</option>
												</Field>
												<ErrorMessage
													as="div"
													:name="`workflows[${index}].to_stage`"
													class="text-danger"
												/>
											</td>
											<td class="align-middle table__td--condition">
												<div class="d-flex align-items-center">
													<Field 
														v-model="setupProcess.condition"
														:name="`workflows[${index}].condition`"
														as="select" 
														class="form-select" 
														@change="changeCondition(index)"
													>
														<option v-for="(optionCondition, indexCondition) in state.selectOptionConditions" :value="optionCondition.value" :key="indexCondition">
															{{ optionCondition.label }}
														</option>
													</Field>
													<Field 
														v-model="setupProcess.condition_status"
														:name="`workflows[${index}].condition_status`"
														as="select" 
														class="form-select ms-2 table__td--condition-status" 
													>
														<option v-for="(optionConditionStatus, indexConditionStatus) in state.selectOptionConditionStatus" :value="optionConditionStatus.value" :key="indexConditionStatus" :disabled="optionConditionStatus.value === ''">
															{{ optionConditionStatus.label }}
														</option>
													</Field>
												</div>
											</td>
											<td class="align-middle table__td--email-template">
												<Field 
													v-model="setupProcess.email_template"
													:name="`workflows[${index}].email_template`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionEmailTemplate, indexEmailTemplate) in state.selectOptionEmailTemplates" :value="optionEmailTemplate.value" :key="indexEmailTemplate">
														{{ optionEmailTemplate.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle table__td--sync-group">
												<Field 
													v-model="setupProcess.sync_group"
													:name="`workflows[${index}].sync_group`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionSyncGroup, indexSyncGroup) in state.selectOptionSyncGroups" :value="optionSyncGroup.value" :key="indexSyncGroup">
														{{ optionSyncGroup.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle table__td--to-stage">
												<Field 
													v-model="setupProcess.back_to_stage"
													:name="`workflows[${index}].back_to_stage`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionFromStage, indexOptionFromStage) in state.selectOptionFromStages" :value="optionFromStage.value" :key="indexOptionFromStage">
														{{ optionFromStage.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle table__td--action-back-to">
												<Field 
													v-model="setupProcess.action_back_to"
													:name="`workflows[${index}].action_back_to`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionActionBackTo, indexActionBackTo) in state.selectOptionActionBackToDefaults" :value="optionActionBackTo.value" :key="indexActionBackTo">
														{{ optionActionBackTo.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle table__td--email-template-back">
												<Field 
													v-model="setupProcess.email_template_back"
													:name="`workflows[${index}].email_template_back`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionEmailTemplate, indexEmailTemplate) in state.selectOptionEmailTemplates" :value="optionEmailTemplate.value" :key="indexEmailTemplate">
														{{ optionEmailTemplate.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle table__td--process-transition">
												<Field 
													v-model="setupProcess.process_transition"
													:name="`workflows[${index}].process_transition`"
													as="select" 
													class="form-select" 
												>
													<option v-for="(optionProcessTransition, indexProcessTransition) in state.selectOptionProcessTransitions" :value="optionProcessTransition.value" :key="indexProcessTransition">
														{{ optionProcessTransition.label }}
													</option>
												</Field>
											</td>
											<td class="align-middle">
												<svg
													xmlns="http://www.w3.org/2000/svg"
													height="24px"
													viewBox="0 -960 960 960"
													width="24px"
													fill="#83868C"
													class="cursor-pointer ms-2"
													@click="removeStageProcess(index)" 
												>
													<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
												</svg>
											</td>
										</tr>
									</tbody>
								</table>
							</CTable>
							<button 
								type="button" 
								class="btn btn-primary mt-2 d-flex align-items-center" 
								@click="addStageProcess()"
							>
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
									<path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
								</svg>
							</button>
						</BAccordionItem>
					</BAccordion>
					<CCardFooter>
						<div class="d-flex justify-content-start">
							<CButton 
								type="submit"
								class="btn btn-primary m-1"
								@click="setSubmitStatus(state.statusActive)"
							>
								<span class="text-uppercase">
									{{ $t('workflow.save_update') }}
								</span>
							</CButton>
							<CButton 
								type="button"
								class="btn btn-light border m-1"
								@click="closeWorkflow"
							>
								<span class="text-uppercase">
									{{ $t('workflow.close') }}
								</span>
							</CButton>
							<CButton 
								type="submit"
								class="btn btn-light border m-1"
								@click="setSubmitStatus(state.statusSaveDraft)"
							>
								<span class="text-uppercase">
									{{ $t('workflow.save_draft') }}
								</span>
							</CButton>
						</div>
					</CCardFooter>
				</Form>
			</CCard>
		</CCol>
	</CRow>
	<Teleport to="body">
		<ContextMenu
			:options="contextMenuOptions"
			:isVisible="isContextMenuVisible"
			:position="contextMenuPosition"
			@selectOption="handleOption"
		/>
	</Teleport>
	
	<loading
		:isLoading="setIsLoading"
	/>
</template>

<script lang="ts">
import { defineComponent, reactive , ref, computed, onMounted } from 'vue'
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';
import { nextTick } from 'vue';
import  { checkInteger, generateStandardSlug } from "@/utils/utils";
import Multiselect from '@vueform/multiselect';
import useFields from '@/composables/field';
import useWorkflows from '@/composables/workflow';
import useOptions from '@/composables/option';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import Swal from 'sweetalert2'
import ContextMenu from '@/views/action/ContextMenu.vue';
import { useContextMenu } from '@/composables/contextMenu';
import { useToast } from 'vue-toast-notification';
import { WORKFLOWS } from "@/constants/constants";
import debounce from 'lodash.debounce';
import ProcessGroupAdd from '@/views/workflow/process-group/ProcessGroupAdd.vue'
import ActionAdd from '@/views/workflow/action/ActionAdd.vue'
import StageAdd from '@/views/workflow/stage/StageAdd.vue'
import EmailTemplateAdd from '@/views/workflow/email-template/EmailTemplateAdd.vue'
import ConditionAdd from '@/views/workflow/condition/ConditionAdd.vue'
import SyncGroupAdd from '@/views/workflow/sync-group/SyncGroupAdd.vue'
import ProcessTransitionAdd from '@/views/workflow/process-transition/ProcessTransitionAdd.vue'
import Loading from '@/views/loading/Loading.vue';
  
export default defineComponent({
    name: "WorkflowAdd",

	components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
		ContextMenu,
		ProcessGroupAdd,
		ActionAdd,
		StageAdd,
		EmailTemplateAdd,
		ConditionAdd,
		SyncGroupAdd,
		ProcessTransitionAdd,
		Loading
    },
  
    setup() {
		const { t }  = useI18n();
		const router = useRouter();
		const $toast = useToast();
		const dynamicForm: any = ref(null);

		// Các options cho context menu của component này
		const contextMenuOptions = computed(() => {
			let menuOptions: any = [];
			const menuOptionFieldSetups = [
				{ label: t('menu_options.field.edit'), action: 'EDIT_FIELD_SETUP', icon: 'edit' },
				{ label: t('menu_options.field.delete'), action: 'DELETE_FIELD_SETUP', icon: 'delete' },
			];

			const menuOptionFieldCreateds = [
				{ label: t('menu_options.field.edit'), action: 'EDIT_FIELD_CREATED', icon: 'edit' },
				{ label: t('menu_options.field.unactive'), action: 'UNACTIVE_FIELD_CREATED', icon: 'cancel' },
			];

			if (state.tabIndex == 0) {
				menuOptions = [...menuOptionFieldCreateds];
			}
			
			if (state.tabIndex == 1) {
				menuOptions = [...menuOptionFieldSetups];
			}

			return menuOptions;
		});

		// Sử dụng composable useContextMenu
		const {
			isContextMenuVisible, 
			contextMenuPosition, 
			selectedField, 
			indexField,
			showContextMenu, 
			startPress, 
			endPress 
		} = useContextMenu();

		const handleOption = (option: any) => {
			if (option.action === 'EDIT_FIELD_CREATED') {
				handleEditFieldCreatedOption();
			} else if (option.action === 'UNACTIVE_FIELD_CREATED') {
				handleUnactiveFieldCreatedOption();
			} else if (option.action === 'EDIT_FIELD_SETUP') {
				handleEditFieldSetupOption();
			} else if (option.action === 'DELETE_FIELD_SETUP') {
				handleDeleteFieldSetupOption();
			}
		};

		// Hiển thị context menu và lưu field được chọn
		const handleContextMenu = (event: any, field: any, index: number) => {
			showContextMenu(event, field, index); // Lưu field vào selectedField
		};

		const handleEditFieldSetupOption = () => {
			if (selectedField.value) {
				state.statusFieldSetupEdit = true;
				state.dataFormField = {...selectedField.value};
				switchTypeDataField(selectedField.value.type);
			}
		};
		const handleDeleteFieldSetupOption = () => {
			if (state.fieldSetups && state.fieldSetups.length) {
				state.fieldSetups.splice(indexField.value, 1);
				state.dataFields = [];
			}
		};

		const transformObject = (obj: object) => {
			return Object.fromEntries(
				Object.entries(obj).map(([key, value]) => {
					if (value === 1) return [key, true];
					if (value === 0) return [key, false];
					if (typeof value === 'string' && isJsonArrayOrObject(value)) {
						return [key, JSON.parse(value)];
					}
					if (typeof value === 'string') {
						return [key, value];
					}
					return [key, value];
				})
			);
		};

		const isJsonArrayOrObject = (str: any) => {
			try {
				const parsed = JSON.parse(str);
				return typeof parsed === 'object' && parsed !== null;
			} catch (e) {
				return false;
			}
		};
		const handleEditFieldCreatedOption = () => {
			state.statusFieldCreatedEdit = true;
			const transformedObject = transformObject(selectedField.value);
			state.dataFormField = {...transformedObject};
			switchTypeDataField(selectedField.value.type);
		};
		const handleUnactiveFieldCreatedOption = () => {
			state.dataFields = [];
			const transformedObject = transformObject(selectedField.value);
			state.dataFormField = {...transformedObject};
			state.dataFormField['is_active'] = false;
			const existingFieldIndex = state.fieldCreatedEdits.findIndex(existingField => existingField.id === state.dataFormField.id);
			if (existingFieldIndex !== -1) {
				state.fieldCreatedEdits.splice(existingFieldIndex, 1, state.dataFormField);
			} else {
				state.fieldCreatedEdits.push(state.dataFormField);
			}
			state.fieldCreateds.splice(indexField.value, 1, state.dataFormField);
		};
        const schema = (typeIsRequired: string) => {
            let schemaForm = yup.object().shape({});

            if (typeIsRequired == "OBJECTSYSTEM") {
                schemaForm = schemaForm.shape({
                    validateMultiselectTable: yup.object()
                        .required(`${t('form.field.table')} ${t('form.validate.required')}`),
                    validateMultiselectTableColumn: yup.object()
                        .required(`${t('form.field.table_column')} ${t('form.validate.required')}`),
                });
            }

            if (typeIsRequired == "TABLE") {
                schemaForm = schemaForm.shape({
					// validateMultiselectChildren: yup.array()
					// 	.test(
					// 		'is-not-empty',
					// 		`${t('form.field.children')} ${t('form.validate.required')}`,
					// 		(value) => Array.isArray(value) && value.length > 0
					// 	)
                    validateMultiselectChildren: yup.array()
						.min(1, `${t('form.field.children')} ${t('form.validate.required')}`)
                        .required(`${t('form.field.children')} ${t('form.validate.required')}`),
                });
            }

			if (typeIsRequired == "SELECT") {
                schemaForm = schemaForm.shape({
                    selectOptions: yup.array()
						.of(yup.string().required(`${t('form.field.options')} ${t('form.validate.required')}`)),
                });
            }

			if (typeIsRequired == "RADIO") {
                schemaForm = schemaForm.shape({
                    selectOptionRadios: yup.array()
						.of(yup.string().required(`${t('form.field.options')} ${t('form.validate.required')}`)),
                });
            }

			if (typeIsRequired == "CHECKLIST") {
                schemaForm = schemaForm.shape({
                    selectOptionChecklists: yup.array()
						.of(yup.string().required(`${t('form.field.options')} ${t('form.validate.required')}`)),
                });
            }
			if (typeIsRequired == "WORKFLOW") {
				schemaForm = schemaForm.shape({
					workflow_name: yup.string()
                        .required(`${t('workflow.name')} ${t('workflow.validate.required')}`),
					process_manager: yup.array()
						.min(1, `${t('workflow.process_manager')} ${t('workflow.validate.required')}`)
                        .required(`${t('workflow.process_manager')} ${t('workflow.validate.required')}`),
					job_manager: yup.array()
						.min(1, `${t('workflow.job_manager')} ${t('workflow.validate.required')}`)
                        .required(`${t('workflow.job_manager')} ${t('workflow.validate.required')}`),
					workflows: yup.array().of(
						yup.object().shape({
							from_stage: yup.object()
                        		.required().typeError(`${t('workflow.stage.from_stage')} ${t('workflow.validate.required')}`),
							action_next: yup.object()
								.required().typeError(`${t('workflow.action.title')} ${t('workflow.validate.required')}`),
							to_stage: yup.object()
							 	.required().typeError(`${t('workflow.stage.to_stage')} ${t('workflow.validate.required')}`),
						})
					),
				})
				
			}
            return schemaForm;
        }

		const initialDataFields = [
			{
				type: 'text',
				label: t('form.field.display_name'),
				floatingLabel: true,
				class: 'form-control',
				name: 'display_name',
				validation: 'required|length:2,100',
				validationMessages: {
					required: `${t('form.field.display_name')} ${t('validate_field.display_name.required')}`,
					length: `${t('form.field.display_name')} ${t('validate_field.display_name.length')}`
				},
			},
			{
				type: 'text',
				label: t('form.field.display_name'),
				floatingLabel: true,
				class: 'form-control',
				name: 'display_name_en',
				validation: 'length:2,100',
				validationMessages: {
					length: `${t('form.field.display_name')} ${t('validate_field.display_name.length')}`
				}
			},
			{
				type: 'checkbox',
				label: t('form.field.required'),
				class: 'form-control',
				name: 'required',
			},
			{
				type: 'checkbox',
				label: t('form.field.multiple_value'),
				class: 'form-control',
				name: 'multiple_value'
			},
			{
				type: 'checkbox',
				label: t('form.field.not_edit'),
				class: 'form-control',
				name: 'not_edit',
			},
			{
				type: 'checkbox',
				label: t('form.field.multiple_file'),
				class: 'form-control',
				name: 'multiple_file'
			},
			{
				type: 'number',
				label: t('form.field.order'),
				floatingLabel: true,
				class: 'form-control',
				name: 'order',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.order')} ${t('validate_field.order.integer')}`
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'text',
				label: t('form.field.placeholder'),
				floatingLabel: true,
				class: 'form-control',
				name: 'placeholder',
				validation: 'length:2,100',
				validationMessages: {
					length: `${t('form.field.placeholder')} ${t('validate_field.placeholder.length')}`
				}
			},
			{
				type: 'text',
				label: t('form.field.placeholder'),
				floatingLabel: true,
				class: 'form-control',
				name: 'placeholder_en',
				validation: 'length:2,100',
				validationMessages: {
					length: `${t('form.field.placeholder')} ${t('validate_field.placeholder.length')}`
				}
			},
			{
				type: 'radio',
				label: t('form.field.column_width'),
				floatingLabel: false,
				options: [
					{ label: t('options_width.2'), value: '2' },
					{ label: t('options_width.4'), value: '4' },
					{ label: t('options_width.6'), value: '6' },
					{ label: t('options_width.8'), value: '8' },
					{ label: t('options_width.10'), value: '10' },
					{ label: t('options_width.12'), value: '12' },
				],
				name: 'column_width',
			},
			{
				type: 'text',
				label: t('form.field.keyword'),
				floatingLabel: true,
				class: 'form-control',
				name: 'keyword',
				validation: 'required|length:2,100',
				validationMessages: {
					required: `${t('form.field.keyword')} ${t('validate_field.keyword.required')}`,
					length: `${t('form.field.keyword')} ${t('validate_field.keyword.length')}`
				}
			},
			{
				type: 'number',
				label: t('form.field.min_character'),
				floatingLabel: true,
				class: 'form-control',
				name: 'min_character',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.min_character')} ${t('validate_field.min_character.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'number',
				label: t('form.field.max_character'),
				floatingLabel: true,
				class: 'form-control',
				name: 'max_character',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.max_character')} ${t('validate_field.max_character.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'number',
				label: t('form.field.min_number'),
				floatingLabel: true,
				class: 'form-control',
				name: 'min_number',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.min_number')} ${t('validate_field.min_number.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'number',
				label: t('form.field.max_number'),
				floatingLabel: true,
				class: 'form-control',
				name: 'max_number',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.max_number')} ${t('validate_field.max_number.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'addable',
				label: t('form.field.options'),
				floatingLabel: true,
				class: 'form-control',
				name: 'options'
			},
			{
				type: 'text',
				label: t('form.field.formula'),
				floatingLabel: true,
				class: 'form-control',
				name: 'formula',
				placeholder: t('form.field.formula_sample'),
				validation: 'required',
				validationMessages: {
					required: `${t('form.field.formula')} ${t('validate_field.formula.required')}`,
				},
			},
			{
				type: 'select',
				label: t('form.field.stages'),
				floatingLabel: false,
				class: 'form-control',
				name: 'stages',
				validation: 'required',
				options: [],
				validationMessages: {
					required: `${t('form.field.stages')} ${t('validate_field.stages.required')}`,
				},
			},
			{
				type: 'text',
				label: t('form.field.default_value'),
				floatingLabel: true,
				class: 'form-control',
				name: 'default_value_varchar',
				validation: 'length:1,200',
				validationMessages: {
					length: `${t('form.field.default_value')} ${t('validate_field.default_value_varchar.length')}`
				}
			},
			{
				type: 'textarea',
				label: t('form.field.default_value'),
				floatingLabel: true,
				class: 'form-control',
				name: 'default_value_text',
				validation: 'length:1,50000',
				validationMessages: {
					length: `${t('form.field.default_value')} ${t('validate_field.default_value_text.length')}`
				}
			},
			{
				type: 'number',
				label: t('form.field.default_value'),
				floatingLabel: true,
				class: 'form-control',
				name: 'default_value_integer',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.default_value')} ${t('validate_field.default_value_integer.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						return checkInteger(Number(value));
					}
				}
			},
			{
				type: 'number',
				label: t('form.field.default_value'),
				floatingLabel: true,
				class: 'form-control',
				name: 'default_value_float',
				validation: 'integer',
				validationMessages: {
					integer: `${t('form.field.default_value')} ${t('validate_field.default_value_float.integer')}`,
				},
				validationRules: {
					integer({ value }) {
						const numberValue = Number(value);
						return numberValue >= 0;
					}
				}
			},
			{
				type: 'date',
				label: t('form.field.default_value'),
				floatingLabel: false,
				class: 'form-control',
				name: 'default_value_date',
			},
			{
				type: 'time',
				label: t('form.field.default_value'),
				floatingLabel: false,
				class: 'form-control',
				name: 'default_value_time',
			},
			{
				type: 'multiselect',
				label: t('form.field.default_value'),
				floatingLabel: false,
				class: 'form-control',
				name: 'default_value_select',
			},
			{
				type: 'user',
				label: t('form.field.default_value'),
				floatingLabel: false,
				class: 'form-control',
				name: 'default_value_user',
			},
			{
				type: 'department',
				label: t('form.field.default_value'),
				floatingLabel: false,
				class: 'form-control',
				name: 'default_value_department',
			},
			{
				type: 'children',
				label: t('form.field.children'),
				floatingLabel: false,
				class: 'form-control',
				name: 'children'
			},
			{
				type: 'table',
				label: t('form.field.table'),
				floatingLabel: true,
				class: 'form-control',
				name: 'table'
			},
			{
				type: 'table_column',
				label: t('form.field.table_column'),
				floatingLabel: true,
				class: 'form-control',
				name: 'table_column'
			},
			{
				type: 'table_column_sub',
				label: t('form.field.table_column_sub'),
				floatingLabel: true,
				class: 'form-control',
				name: 'table_column_sub'
			},
		] as Array<any>;

		const state = reactive({
			statusSubmit: '',
			statusActive: WORKFLOWS.STATUS.ACTIVE,
			statusSaveDraft: WORKFLOWS.STATUS.SAVE_DRAFT,
			stringActionNext: WORKFLOWS.STRING.ACTION_NEXT,
			stringStage: WORKFLOWS.STRING.STAGE,
			stringCondition: WORKFLOWS.STRING.CONDITION,
			stringSyncGroup: WORKFLOWS.STRING.SYNC_GROUP,
			stringEmailTemplate:  WORKFLOWS.STRING.EMAIL_TEMPLATE,
			stringProcessTransition:  WORKFLOWS.STRING.PROCESS_TRANSITION,
			showModalProcessGroup: false,
			showModalForm: false,
			showModalAction: false,
			showModalStage: false,
			showModalCondition: false,
			showModalEmailTemplate: false,
			showModalProcessTransition: false,
			showModalSyncGroup: false,
			showComponentModal: false,
			statusFieldSetupEdit: false,
			statusFieldCreatedEdit: false,
			validateSubmitForm: false,
			tabIndex: 1,
			nameFieldDefault: ['display_name', 'required', 'order', 'placeholder', 'column_width', 'keyword', 'not_edit', 'stages'],
			nameFieldTypeVarchar: ['min_character', 'max_character', 'default_value_varchar'],
			nameFieldTypeText: ['min_character', 'max_character', 'default_value_text'],
			nameFieldTypeInteger: ['min_number', 'max_number', 'default_value_integer'],
			nameFieldTypeFloat: ['default_value_float'],
			nameFieldTypeDate: ['default_value_date'],
			nameFieldTypeTime: ['default_value_time'],
			nameFieldTypeSelect: ['options', 'multiple_value', 'default_value_select'],
			nameFieldTypeRadio: ['options', 'default_value_select'],
			nameFieldTypeChecklist: ['options', 'default_value_select'],
			nameFieldTypeUser: ['multiple_value', 'default_value_user'],
			nameFieldTypeDepartment: ['multiple_value', 'default_value_department'],
			nameFieldTypeFile: ['display_name', 'required', 'order', 'column_width', 'keyword', 'multiple_file', 'stages'],
			nameFieldTypeFormula: ['display_name', 'order', 'column_width', 'keyword', 'formula', 'stages'],
			nameFieldTypeTable: ['display_name', 'order', 'column_width', 'keyword', 'children', 'stages'],
			nameFieldTypeObjectSystem: ['multiple_value', 'table', 'table_column', 'table_column_sub'],
			fieldSetups: [] as Array<any>,
			fieldCreateds: [] as Array<any>,
			fieldCreatedEdits: [] as Array<any>,
			dataWorkflow: {} as any,
			dataWorkflowVersion: {} as any,
			dataFormWorkflow: {} as any,
			dataFormField: {} as any,
			dataFields: [] as Array<any>,
			dataFieldEns: [] as Array<any>,
			selectOptions: ['Lựa chọn 1', 'Lựa chọn 2'] as Array<any>,
			selectOptionRadios: ['Lựa chọn 1', 'Lựa chọn 2'] as Array<any>,
			selectOptionChecklists: ['Lựa chọn 1', 'Lựa chọn 2'] as Array<any>,
            selectOptionTables: [] as Array<any>,
            selectOptionTableColumns: [] as Array<any>,
			defaultMultiSelectedOptions: [] as Array<any>,
			defaultSelectedOptions: null,
			selectOptionDepartments: [] as Array<any>,
			defaultMultiSelectedOptionUsers: [] as Array<any>,
			defaultSelectedOptionUsers: {} as any,
			defaultMultiSelectedOptionDepartments: [] as Array<any>,
			defaultSelectedOptionDepartments: {} as any,
            defaultSelectedOptionTable: {} as any,
            defaultSelectedOptionTableColumn: {} as any,
			defaultSelectedOptionTableColumnSub: [] as Array<any>,
			dataProcessGroup: {
				name: '',
			},
			dataAction: {
				name: '',
				slug: '',
				description: '',
			},
			dataStage: {
				name: '',
				slug: '',
				description: '',
				approver_ids: [] as Array<any>,
				follower_ids: [] as Array<any>,
				comment: '',
			},
			dataEmailTemplate: {
				name: '',
				slug: '',
				condition: '',
				from_email: '',
				title_name: '',
				to_emails: [] as Array<any>,
				cc_emails: [] as Array<any>,
				bcc_emails: [] as Array<any>,
				content: '',
				files: [] as Array<any>,
			},
			dataCondition: {
				name: '',
				slug: '',
				orConditions: [],
				arrayConditions: [] as Array<any>,
				arrayConditionSubs: [] as Array<any>,
			},
			dataSyncGroup: {
				name: '',
				slug: '',
			},
			dataProcessTransition: {
				name: '',
				slug: '',
				apply_process: '',
				type_create: 'auto',
			},
			selectOptionActionNextDefaults: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.action.option.create'), value: { action_next: WORKFLOWS.ACTION.CREATE } },
			] as Array<any>,
			selectOptionActionBackToDefaults: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.action.option.back_to'), value: { action_back_to: WORKFLOWS.ACTION.BACK_TO } },
			] as Array<any>,
			selectOptionConditionDefaults: [
				{ label: t('workflow.choose'), value: '' },
			] as Array<any>,
			selectOptionFromStageDefaults: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.stage.option.start'), value: { from_stage: WORKFLOWS.STAGE.START } },
			] as Array<any>,
			selectOptionToStageDefaults: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.stage.option.done'), value: { stage_done: WORKFLOWS.STAGE.DONE } },
				{ label: t('workflow.stage.option.false'), value: { stage_false: WORKFLOWS.STAGE.FALSE } },
			] as Array<any>,
			selectOptionSyncGroupDefaults: [
				{ label: t('workflow.choose'), value: '' },
			] as Array<any>,
			selectOptionProcessTransitionDefaults: [
				{ label: t('workflow.choose'), value: '' },
			] as Array<any>,
			selectOptionEmailTemplateDefaults: [
				{ label: t('workflow.choose'), value: '' },
			] as Array<any>,
			selectOptionConditionStatus: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.condition.status.success'), value: WORKFLOWS.CONDITION.TRUE },
				{ label: t('workflow.condition.status.failure'), value: WORKFLOWS.CONDITION.FALSE },
			] as Array<any>,
			selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
			] as Array<any>,
			selectOptionFromStages: [] as Array<any>,
			selectOptionToStages: [] as Array<any>,
			selectOptionActionNexts: [] as Array<any>,
			listDataStages: [] as Array<any>,
			listDataActions: [] as Array<any>,
			listDataConditions: [] as Array<any>,
			listDataSyncGroups: [] as Array<any>,
			listDataProcessTransitions: [] as Array<any>,
			listDataEmailTemplates: [] as Array<any>,
			selectOptionEmailTemplates: [] as Array<any>,
			selectOptionConditions: [] as Array<any>,
			selectOptionSyncGroups: [] as Array<any>,
			selectOptionProcessTransitions: [] as Array<any>,
			dataSetupProcess: [] as Array<any>,
			typeFields: [
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M430-160v-540H200v-100h560v100H530v540H430Z',
						],
					},
					label: 'type_field.varchar',
					desc: 'type_field.varchar_desc',
					type: 'VARCHAR',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M278-279.33h276.67V-346H278v66.67Zm0-167.34h404v-66.66H278v66.66ZM278-614h404v-66.67H278V-614Zm-91.33 494q-27 0-46.84-19.83Q120-159.67 120-186.67v-586.66q0-27 19.83-46.84Q159.67-840 186.67-840h586.66q27 0 46.84 19.83Q840-800.33 840-773.33v586.66q0 27-19.83 46.84Q800.33-120 773.33-120H186.67Zm0-66.67h586.66v-586.66H186.67v586.66Zm0-586.66v586.66-586.66Z',
						],
					},
					label: 'type_field.text',
					desc: 'type_field.text_desc',
					type: 'TEXT',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M120-80v-60h100v-30h-60v-60h60v-30H120v-60h120q17 0 28.5 11.5T280-280v40q0 17-11.5 28.5T240-200q17 0 28.5 11.5T280-160v40q0 17-11.5 28.5T240-80H120Zm0-280v-110q0-17 11.5-28.5T160-510h60v-30H120v-60h120q17 0 28.5 11.5T280-560v70q0 17-11.5 28.5T240-450h-60v30h100v60H120Zm60-280v-180h-60v-60h120v240h-60Zm186 434v-66.67h474V-206H366Zm0-242v-66.67h474V-448H366Zm0-242v-66.67h474V-690H366Z',
						],
					},
					label: 'type_field.integer',
					desc: 'type_field.integer_desc',
					type: 'INTEGER',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M289.9-540q-53.9 0-91.9-38.1-38-38.1-38-92t38.1-91.9q38.1-38 92-38t91.9 38.1q38 38.1 38 92T381.9-578q-38.1 38-92 38Zm-.02-60q29.12 0 49.62-20.38 20.5-20.38 20.5-49.5t-20.38-49.62q-20.38-20.5-49.5-20.5t-49.62 20.38q-20.5 20.38-20.5 49.5t20.38 49.62q20.38 20.5 49.5 20.5ZM669.9-160q-53.9 0-91.9-38.1-38-38.1-38-92t38.1-91.9q38.1-38 92-38t91.9 38.1q38 38.1 38 92T761.9-198q-38.1 38-92 38Zm-.02-60q29.12 0 49.62-20.38 20.5-20.38 20.5-49.5t-20.38-49.62q-20.38-20.5-49.5-20.5t-49.62 20.38q-20.5 20.38-20.5 49.5t20.38 49.62q20.38 20.5 49.5 20.5ZM202-160l-42-42 598-598 42 42-598 598Z',
						],
					},
					label: 'type_field.float',
					desc: 'type_field.float_desc',
					type: 'FLOAT',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M306-394q-17 0-28.5-11.5T266-434q0-17 11.5-28.5T306-474q17 0 28.5 11.5T346-434q0 17-11.5 28.5T306-394Zm177 0q-17 0-28.5-11.5T443-434q0-17 11.5-28.5T483-474q17 0 28.5 11.5T523-434q0 17-11.5 28.5T483-394Zm170 0q-17 0-28.5-11.5T613-434q0-17 11.5-28.5T653-474q17 0 28.5 11.5T693-434q0 17-11.5 28.5T653-394ZM180-80q-24 0-42-18t-18-42v-620q0-24 18-42t42-18h65v-60h65v60h340v-60h65v60h65q24 0 42 18t18 42v620q0 24-18 42t-42 18H180Zm0-60h600v-430H180v430Zm0-490h600v-130H180v130Zm0 0v-130 130Z',
						],
					},
					label: 'type_field.date',
					desc: 'type_field.date_desc',
					type: 'DATE',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M360-860v-60h240v60H360Zm90 447h60v-230h-60v230Zm30 332q-74 0-139.5-28.5T226-187q-49-49-77.5-114.5T120-441q0-74 28.5-139.5T226-695q49-49 114.5-77.5T480-801q67 0 126 22.5T711-716l51-51 42 42-51 51q36 40 61.5 97T840-441q0 74-28.5 139.5T734-187q-49 49-114.5 77.5T480-81Zm0-60q125 0 212.5-87.5T780-441q0-125-87.5-212.5T480-741q-125 0-212.5 87.5T180-441q0 125 87.5 212.5T480-141Zm0-299Z',
						],
					},
					label: 'type_field.time',
					desc: 'type_field.time_desc',
					type: 'TIME',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M290-620v-60h550v60H290Zm0 170v-60h550v60H290Zm0 170v-60h550v60H290ZM150-620q-12 0-21-9t-9-21.5q0-12.5 9-21t21.5-8.5q12.5 0 21 8.62 8.5 8.63 8.5 21.38 0 12-8.62 21-8.63 9-21.38 9Zm0 170q-12 0-21-9t-9-21.5q0-12.5 9-21t21.5-8.5q12.5 0 21 8.62 8.5 8.63 8.5 21.38 0 12-8.62 21-8.63 9-21.38 9Zm0 170q-12 0-21-9t-9-21.5q0-12.5 9-21t21.5-8.5q12.5 0 21 8.62 8.5 8.63 8.5 21.38 0 12-8.62 21-8.63 9-21.38 9Z',
						],
					},
					label: 'type_field.select',
					desc: 'type_field.select_desc',
					type: 'SELECT',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M480-80q-82 0-155-31.5t-127.5-86Q143-252 111.5-325T80-480q0-83 31.5-156t86-127Q252-817 325-848.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 82-31.5 155T763-197.5q-54 54.5-127 86T480-80Zm0-60q142 0 241-99.5T820-480q0-142-99-241t-241-99q-141 0-240.5 99T140-480q0 141 99.5 240.5T480-140Zm0-340Z',
						],
					},
					label: 'type_field.radio',
					desc: 'type_field.radio_desc',
					type: 'RADIO',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M180-120q-24 0-42-18t-18-42v-600q0-24 18-42t42-18h600q24 0 42 18t18 42v600q0 24-18 42t-42 18H180Zm0-60h600v-600H180v600Z',
						],
					},
					label: 'type_field.checklist',
					desc: 'type_field.checklist_desc',
					type: 'CHECKLIST',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M480-481q-66 0-108-42t-42-108q0-66 42-108t108-42q66 0 108 42t42 108q0 66-42 108t-108 42ZM160-160v-94q0-38 19-65t49-41q67-30 128.5-45T480-420q62 0 123 15.5t127.92 44.69q31.3 14.13 50.19 40.97Q800-292 800-254v94H160Zm60-60h520v-34q0-16-9.5-30.5T707-306q-64-31-117-42.5T480-360q-57 0-111 11.5T252-306q-14 7-23 21.5t-9 30.5v34Zm260-321q39 0 64.5-25.5T570-631q0-39-25.5-64.5T480-721q-39 0-64.5 25.5T390-631q0 39 25.5 64.5T480-541Zm0-90Zm0 411Z',
						],
					},
					label: 'type_field.user',
					desc: 'type_field.user_desc',
					type: 'USER',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M480-60q-63 0-106.5-43.5T330-210q0-55 34.5-95.5T450-357v-93H210v-150H100v-280h280v280H270v90h420v-93q-51-11-85.5-51.5T570-750q0-63 43.5-106.5T720-900q63 0 106.5 43.5T870-750q0 55-34.5 95.5T750-603v153H510v93q51 11 85.5 51.5T630-210q0 63-43.5 106.5T480-60Zm239.8-600q37.2 0 63.7-26.3t26.5-63.5q0-37.2-26.3-63.7T720.2-840q-37.2 0-63.7 26.3T630-750.2q0 37.2 26.3 63.7t63.5 26.5ZM160-660h160v-160H160v160Zm319.8 540q37.2 0 63.7-26.3t26.5-63.5q0-37.2-26.3-63.7T480.2-300q-37.2 0-63.7 26.3T390-210.2q0 37.2 26.3 63.7t63.5 26.5ZM240-740Zm480-10ZM480-210Z',
						],
					},
					label: 'type_field.department',
					desc: 'type_field.department_desc',
					type: 'DEPARTMENT',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M250-160q-86 0-148-62T40-370q0-78 49.5-137.5T217-579q20-97 94-158.5T482-799q113 0 189.5 81.5T748-522v24q72-2 122 46.5T920-329q0 69-50 119t-119 50H510q-24 0-42-18t-18-42v-258l-83 83-43-43 156-156 156 156-43 43-83-83v258h241q45 0 77-32t32-77q0-45-32-77t-77-32h-63v-84q0-89-60.5-153T478-739q-89 0-150 64t-61 153h-19q-62 0-105 43.5T100-371q0 62 43.93 106.5T250-220h140v60H250Zm230-290Z',
						],
					},
					label: 'type_field.file_upload',
					desc: 'type_field.file_upload_desc',
					type: 'FILEUPLOAD',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M180-120q-24 0-42-18t-18-42v-600q0-24 18-42t42-18h600q24 0 42 18t18 42v600q0 24-18 42t-42 18H180Zm0-60h600v-600L180-180Zm540-130H494v-50h226v50ZM224-610h76v76h50v-76h76v-50h-76v-76h-50v76h-76v50Z',
						],
					},
					label: 'type_field.formula',
					desc: 'type_field.formula_desc',
					type: 'FORMULA',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M453-280h60v-166h167v-60H513v-174h-60v174H280v60h173v166Zm27.27 200q-82.74 0-155.5-31.5Q252-143 197.5-197.5t-86-127.34Q80-397.68 80-480.5t31.5-155.66Q143-709 197.5-763t127.34-85.5Q397.68-880 480.5-880t155.66 31.5Q709-817 763-763t85.5 127Q880-563 880-480.27q0 82.74-31.5 155.5Q817-252 763-197.68q-54 54.31-127 86Q563-80 480.27-80Zm.23-60Q622-140 721-239.5t99-241Q820-622 721.19-721T480-820q-141 0-240.5 98.81T140-480q0 141 99.5 240.5t241 99.5Zm-.5-340Z',
						],
					},
					label: 'type_field.table',
					desc: 'type_field.table_desc',
					type: 'TABLE',
				},
				{
					svg: {
						xmlns: 'http://www.w3.org/2000/svg',
						height: '30px',
						viewBox: '0 -960 960 960',
						width: '30px',
						fill: '#83868C',
						paths: [
							'M480-120q-151 0-255.5-46.5T120-280v-400q0-66 105.5-113T480-840q149 0 254.5 47T840-680v400q0 67-104.5 113.5T480-120Zm0-488q86 0 176.5-26.5T773-694q-27-32-117.5-59T480-780q-88 0-177 26t-117 60q28 35 116 60.5T480-608Zm-1 214q42 0 84-4.5t80.5-13.5q38.5-9 73.5-22t63-29v-155q-29 16-64 29t-74 22q-39 9-80 14t-83 5q-42 0-84-5t-80.5-14q-38.5-9-73-22T180-618v155q27 16 61 29t72.5 22q38.5 9 80.5 13.5t85 4.5Zm1 214q48 0 99-8.5t93.5-22.5q42.5-14 72-31t35.5-35v-125q-28 16-63 28.5T643.5-352q-38.5 9-80 13.5T479-334q-43 0-85-4.5T313.5-352q-38.5-9-72.5-21.5T180-402v126q5 17 34 34.5t72 31q43 13.5 94 22t100 8.5Z',
						],
					},
					label: 'type_field.object_system',
					desc: 'type_field.object_system_desc',
					type: 'OBJECTSYSTEM',
				},
			] as Array<any>,
		});

		const { getTables, getColumns } = useFields();
        const { getUsers, getDepartments, getProcessGroups, getScopes } = useOptions();
		const { setIsLoading, storeWorkflow } = useWorkflows();

		const processedDataFields = computed(() => {
			return initialDataFields.map(field => {
				const isRequired = field.validation && field.validation.includes('required');
				return {
					...field,
					labelClass: isRequired ? 'required-label' : '',
				};
			});
		});
		
		const selectTypeField = async (typeField: any) => {
			state.tabIndex = 1;
			state.dataFormField = {};
			state.statusFieldSetupEdit = false;
			state.statusFieldCreatedEdit = false;
			state.defaultSelectedOptions = null;
			state.defaultMultiSelectedOptions = [];
            state.dataFormField['column_width'] = 6;
			state.dataFormField['display_name'] = t(typeField.label);
			state.dataFormField['order'] = state.fieldSetups.length + 1;
			snakeCaseDisplayName();
			switchTypeDataField(typeField.type);
		}

		const switchTypeDataField = async (typeField: string) => {
			const typeNotPlaceholder = ['FILEUPLOAD', 'TABLE', 'FORMULA'];
			const typeValidationForm = ['OBJECTSYSTEM', 'TABLE', 'SELECT', 'RADIO', 'CHECKLIST'];
			typeValidationForm.includes(typeField) ? state.validateSubmitForm = true : state.validateSubmitForm = false;
			state.dataFieldEns = processedDataFields.value.filter(field => typeNotPlaceholder.includes(typeField) ? ['display_name_en'].includes(field.name) : ['display_name_en', 'placeholder_en'].includes(field.name));
			state.dataFormField['type'] = typeField;
			switch (typeField) {
				case 'VARCHAR':
					state.dataFormField['label_type'] = t('type_field.varchar');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_varchar'] = state.dataFormField['default_value'];
						state.dataFormField['min_character'] = state.dataFormField['min_equal'];
						state.dataFormField['max_character'] = state.dataFormField['max_equal'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_varchar'];
						state.dataFormField['min_equal'] = state.dataFormField['min_character'];
						state.dataFormField['max_equal'] = state.dataFormField['max_character'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeVarchar].includes(field.name));
					break;
				case 'TEXT':
					state.dataFormField['label_type'] = t('type_field.text');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_text'] = state.dataFormField['default_value'];
						state.dataFormField['min_character'] = state.dataFormField['min_equal'];
						state.dataFormField['max_character'] = state.dataFormField['max_equal'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_text'];
						state.dataFormField['min_equal'] = state.dataFormField['min_character'];
						state.dataFormField['max_equal'] = state.dataFormField['max_character'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeText].includes(field.name));
					break;
				case 'INTEGER':
					state.dataFormField['label_type'] = t('type_field.integer');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_integer'] = state.dataFormField['default_value'];
						state.dataFormField['min_number'] = state.dataFormField['min_equal'];
						state.dataFormField['max_number'] = state.dataFormField['max_equal'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_integer'];
						state.dataFormField['min_equal'] = state.dataFormField['min_number'];
						state.dataFormField['max_equal'] = state.dataFormField['max_number'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeInteger].includes(field.name));
					break;
				case 'FLOAT':
					state.dataFormField['label_type'] = t('type_field.float');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_float'] = state.dataFormField['default_value'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_float'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeFloat].includes(field.name));
					break;
				case 'DATE':
					state.dataFormField['label_type'] = t('type_field.date');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_date'] = state.dataFormField['default_value'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_date'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeDate].includes(field.name));
					break;
				case 'TIME':
					state.dataFormField['label_type'] = t('type_field.time');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['default_value_time'] = state.dataFormField['default_value'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['default_value_time'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeTime].includes(field.name));
					break;
				case 'SELECT':
					state.dataFormField['label_type'] = t('type_field.select');
					if (state.statusFieldCreatedEdit) {
						state.selectOptions = [...state.dataFormField['options']];
						state.dataFormField['multiple_value'] = state.dataFormField['multiple'];
						state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptions = state.dataFormField['default_value'] : state.defaultSelectedOptions = state.dataFormField['default_value'];
					} else {
						state.dataFormField['multiple'] = state.dataFormField['multiple_value'];
						state.dataFormField['options'] = [...state.selectOptions];
						state.dataFormField['default_value'] = state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptions : state.defaultSelectedOptions;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeSelect].includes(field.name));
					break;
				case 'RADIO':
					state.dataFormField['label_type'] = t('type_field.radio');
					if (state.statusFieldCreatedEdit) {
						state.selectOptionRadios = [...state.dataFormField['options']];
						state.defaultSelectedOptions = state.dataFormField['default_value'];
					} else {
						state.dataFormField['options'] = [...state.selectOptionRadios];
						state.dataFormField['default_value'] = state.defaultSelectedOptions;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeRadio].includes(field.name));
					break;
				case 'CHECKLIST':
					state.dataFormField['label_type'] = t('type_field.checklist');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['multiple_value'] = state.dataFormField['multiple'];
						state.selectOptionChecklists = [...state.dataFormField['options']];
						state.defaultMultiSelectedOptions = state.dataFormField['default_value'];
					} else {
						state.dataFormField['options'] = [...state.selectOptionChecklists];
						state.dataFormField['multiple_value'] = true;
						state.dataFormField['multiple'] = state.dataFormField['multiple_value'];
						state.dataFormField['default_value'] = state.defaultMultiSelectedOptions;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeChecklist].includes(field.name));
					break;
				case 'USER':
					state.dataFormField['label_type'] = t('type_field.user');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['multiple_value'] = state.dataFormField['multiple'];
						state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptionUsers = state.dataFormField['default_value'] : state.defaultSelectedOptionUsers = state.dataFormField['default_value'];
					} else {
						state.dataFormField['multiple'] = state.dataFormField['multiple_value'];
						state.dataFormField['default_value'] = state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptionUsers : state.defaultSelectedOptionUsers;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeUser].includes(field.name));
					break;
				case 'DEPARTMENT':
					await getOptionDepartments();
					state.dataFormField['label_type'] = t('type_field.department');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['multiple_value'] = state.dataFormField['multiple'];
						state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptionDepartments = state.dataFormField['default_value'] : state.defaultSelectedOptionDepartments = state.dataFormField['default_value'];
					} else {
						state.dataFormField['multiple'] = state.dataFormField['multiple_value'];
						state.dataFormField['default_value'] = state.dataFormField['multiple_value'] ? state.defaultMultiSelectedOptionDepartments : state.defaultSelectedOptionDepartments;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeDepartment].includes(field.name));
					break;
				case 'FILEUPLOAD':
					state.dataFormField['label_type'] = t('type_field.file_upload');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['multiple_file'] = state.dataFormField['multiple'];
					} else {
						state.dataFormField['multiple'] = state.dataFormField['multiple_file'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldTypeFile].includes(field.name));
					break;
				case 'FORMULA':
					state.dataFormField['label_type'] = t('type_field.formula');
					if (state.statusFieldCreatedEdit) {
						state.dataFormField['formula'] = state.dataFormField['default_value'];
					} else {
						state.dataFormField['default_value'] = state.dataFormField['formula'];
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldTypeFormula].includes(field.name));
					break;
				case 'TABLE':
					state.dataFormField['label_type'] = t('type_field.table');
					if (state.statusFieldCreatedEdit) {
						state.defaultMultiSelectedOptions = state.dataFormField['children'].map((child: any) => ({ value: child.keyword, label: child.display_name }));
					} else {
						state.dataFormField['childrens'] = state.defaultMultiSelectedOptions;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldTypeTable].includes(field.name));
					break;
				case 'OBJECTSYSTEM':
                    await getOptionTables();
					state.dataFormField['label_type'] = t('type_field.object_system');
					if (state.statusFieldCreatedEdit) {
						state.defaultSelectedOptionTable = state.selectOptionTables.find((option) => option.value === state.dataFormField['object_table']);
						await changeOptionTable(state.defaultSelectedOptionTable);
						state.defaultSelectedOptionTableColumn = state.selectOptionTableColumns.find((option) => option.value === state.dataFormField['column_table']);
						state.defaultSelectedOptionTableColumnSub = state.selectOptionTableColumns.find((option) => option.value === state.dataFormField['column_table_sub']);
					} else {
						state.dataFormField['object_table'] = state.defaultSelectedOptionTable.value;
                    	state.dataFormField['column_table'] = state.defaultSelectedOptionTableColumn.value;
						state.dataFormField['column_table_sub'] = state.defaultSelectedOptionTableColumnSub;
					}
					state.dataFields = processedDataFields.value.filter((field: any) => [...state.nameFieldDefault, ...state.nameFieldTypeObjectSystem].includes(field.name));
					break;
				default:
					break;
			}
		}

		const handleInputDisplayName = async (fieldName: string, value: any, fieldType: string) => {
			if (fieldName === 'multiple_value' && value === true && fieldType === 'OBJECTSYSTEM') {
				state.defaultSelectedOptionTableColumnSub = [];
			}
			if (fieldName === 'display_name') {
				// state.dataFormField['display_name'] = value;
				await nextTick();
				snakeCaseDisplayName();
			}
		};

		const snakeCaseDisplayName = () => {
			if (state.dataFormField['display_name']) {
				const slug = generateStandardSlug(state.dataFormField['display_name']);
    			state.dataFormField['keyword'] = slug;
			} else {
				state.dataFormField['keyword'] = '';
			}
		};

		const addItemSelectOptions = (typeField: string): void => {
			if (typeField == 'SELECT') {
				state.selectOptions.push(`Lựa chọn ${state.selectOptions.length + 1}`);
			}

			if (typeField == 'RADIO') {
				state.selectOptionRadios.push(`Lựa chọn ${state.selectOptionRadios.length + 1}`);
			}

			if (typeField == 'CHECKLIST') {
				state.selectOptionChecklists.push(`Lựa chọn ${state.selectOptionChecklists.length + 1}`);
			}
		}

		const removeSelectOptions = (index: number, typeField: string): void => {
            if (index !== 0) {
				if (typeField == 'SELECT') {
					state.selectOptions.splice(index, 1);
				}
                
				if (typeField == 'RADIO') {
					state.selectOptionRadios.splice(index, 1);
				}

				if (typeField == 'CHECKLIST') {
					state.selectOptionChecklists.splice(index, 1);
				}
            }
		}

		const closeField = () => {
			state.tabIndex = 1;
			state.dataFields = [];
		}

		const submitForm = () => {
			const node = dynamicForm.value.node
			node.submit()
		}

		const setSubmitStatus = (status: string) => {
			state.statusSubmit = status;
		}

		const handleSubmitForm = async () => {
			if (state.fieldSetups.length === 0) {
                $toast.open({
                    message: t('toast.message.field_not_setup'),
                    type: "warning",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });

                return false;
            }

			closeModalForm();
		}

		const handleSubmitWorkflow = async () => {
			if (state.fieldSetups.length === 0) {
                $toast.open({
                    message: t('toast.message.field_not_setup'),
                    type: "warning",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });

                return false;
            }
			const result = await Swal.fire({
				title: t('swal.title'),
				text: `${t('swal.text')}`,
				icon: 'question',
				showCancelButton: true,
				confirmButtonText: t('swal.confirm_button_text'),
				cancelButtonText: t('swal.cancel_button_text'),
				confirmButtonColor: '#3085d6',
				cancelButtonColor: '#d33',
				reverseButtons: true,
			});
			if (result.isConfirmed) {
				console.log(state.dataSetupProcess, 'dataSetupProcess');
				console.log(state.dataFormWorkflow, 'dataFormWorkflow');
				console.log(state.dataWorkflow, 'dataWorkflow');
				console.log(state.dataWorkflowVersion, 'dataWorkflowVersion');
				console.log(state.fieldSetups, 'fieldSetups');
				const processedEmailTemplates = new Set(); // Mảng theo dõi các email templates đã xử lý
				const processedEmailTemplatesBack = new Set(); // Mảng theo dõi các email templates back đã xử lý
				let formData = new FormData();
				formData.append('dataFormWorkflow', JSON.stringify(state.dataFormWorkflow));
				formData.append('fieldSetups', JSON.stringify(state.fieldSetups));
				formData.append('dataWorkflow', JSON.stringify(state.dataWorkflow));
				formData.append('dataWorkflowVersion', JSON.stringify(state.dataWorkflowVersion));
				formData.append('dataSetupProcess', JSON.stringify(state.dataSetupProcess));
				formData.append('statusSubmit', state.statusSubmit);
				// Duyệt qua state.dataSetupProcess để thêm file từ email_template
				state.dataSetupProcess.forEach((setupProcess, index) => {
					if (setupProcess.email_template && setupProcess.email_template.files) {
						const emailTemplateName = setupProcess.email_template.name;

						// Kiểm tra nếu email template đã được xử lý
						if (!processedEmailTemplates.has(emailTemplateName)) {
							processedEmailTemplates.add(emailTemplateName); // Đánh dấu email template đã xử lý

							// Thêm từng file vào FormData
							setupProcess.email_template.files.forEach((file: any, fileIndex: number) => {
								formData.append(`emailTemplateFiles[${index}][${fileIndex}]`, file);
							});
						}
					}
					// Duyệt qua state.dataSetupProcess để thêm file từ email_template_back
					if (setupProcess.email_template_back && setupProcess.email_template_back.files) {
						const emailTemplateBackName = setupProcess.email_template_back.name;
						// Kiểm tra nếu email template back đã được xử lý
						if (!processedEmailTemplatesBack.has(emailTemplateBackName)) {
							processedEmailTemplatesBack.add(emailTemplateBackName); // Đánh dấu email template back đã xử lý
							// Thêm từng file vào FormData
							setupProcess.email_template_back.files.forEach((file: any, fileIndex: number) => {
								formData.append(`emailTemplateBackFiles[${index}][${fileIndex}]`, file);
							});
						}
					}
				});
				
				let response: any = await storeWorkflow(formData);
				
				if (response) {
					if (response.status === 'success') {
						$toast.open({
							message: t('toast.status.ACTION_SUCCESS'),
							type: "success",
							duration: 5000,
							dismissible: true,
							position: "bottom-right",
						});
						
						// await router.push({ name: 'WorkflowDetail', params: { id: response.process_id } });
					}
				}
			}
		}

		const handleSubmitField = async (typeField: string) => {
			if (state.tabIndex == 0) {
				state.dataFormField['is_active'] = true;
				state.statusFieldCreatedEdit = false;
				switchTypeDataField(typeField);
				const existingFieldIndex = state.fieldCreatedEdits.findIndex(existingField => existingField.id === state.dataFormField.id);
				if (existingFieldIndex !== -1) {
					state.fieldCreatedEdits.splice(existingFieldIndex, 1, state.dataFormField);
				} else {
					state.fieldCreatedEdits.push(state.dataFormField);
				}
				state.fieldCreateds.splice(indexField.value, 1, state.dataFormField);
			} else {
				switchTypeDataField(typeField);
				if (state.statusFieldSetupEdit) {
					state.fieldSetups.splice(indexField.value, 1, state.dataFormField);
					$toast.open({
						message: t('toast.status.ACTION_SUCCESS'),
						type: "success",
						duration: 5000,
						dismissible: true,
						position: "bottom-right",
					});
				} else {
					console.log(state.dataFormField);
					
					const keywordExists = state.fieldSetups.some(existingField => existingField.keyword === state.dataFormField.keyword);
					if (!keywordExists) {
						state.fieldSetups.push(state.dataFormField);
						$toast.open({
							message: t('toast.status.ACTION_SUCCESS'),
							type: "success",
							duration: 5000,
							dismissible: true,
							position: "bottom-right",
						});
					} else {
						$toast.open({
							message: `${t('form.field.keyword')} ${state.dataFormField.keyword} ${t('swal.already_exists')}!`,
							type: "warning",
							duration: 5000,
							dismissible: true,
							position: "bottom-right",
						});

						return false; 
					}
				}
			}
		}

		const getFieldCreated = async () => {
			isContextMenuVisible.value = false;
		}

		const hiddenContextMenuVisible = () => {
			isContextMenuVisible.value = false;
		}

		onMounted( async () => {
			addStageProcess();
			state.selectOptionFromStages = [...state.selectOptionFromStageDefaults];
			state.selectOptionToStages = [...state.selectOptionToStageDefaults];
			state.selectOptionActionNexts = [...state.selectOptionActionNextDefaults];
			state.selectOptionConditions = [...state.selectOptionConditionDefaults];
			state.selectOptionSyncGroups = [...state.selectOptionSyncGroupDefaults];
			state.selectOptionEmailTemplates = [...state.selectOptionEmailTemplateDefaults];
			state.selectOptionProcessTransitions = [...state.selectOptionProcessTransitionDefaults];
			updateOptionTypeSelectInitialDataFields(state.selectOptionFromStages);
		});
		
		const selectOptionChildrens = computed(() => {
			return [...state.fieldCreateds, ...state.fieldSetups]
				.filter((field: any) => field.type !== 'RADIO' && field.type !== 'CHECKLIST' && field.type !== 'TABLE')
				.map((field: any, index: number) => ({
					label: field.display_name,
					value: field.keyword,
				}));
		});

		const getOptionProcessGroups = async (query: string) => {
			let result = await getProcessGroups(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: elem.name,
                    } 
                ));
            }
		}

		const debouncedGetOptionProcessGroups = debounce(getOptionProcessGroups, 500);

		const getOptionProcessScopes = async (query: string) => {
            let result = await getScopes(query);
            if (Array.isArray(result) && result.length > 0) {
                // Kết hợp các option mặc định với kết quả trả về từ API
                return [...state.selectOptionSystemDefaults, ...result];
            }

            // Nếu không có kết quả từ API, chỉ trả về các option mặc định
            return [...state.selectOptionSystemDefaults];
		}

		const debouncedGetOptionScopes = debounce(getOptionProcessScopes, 500);

        const getOptionUsers = async (query: string) => {
			let result = await getUsers(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: `${elem.account_name} - ${elem.full_name}`,
                    } 
                ));
            }
		}

		const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

		const getOptionDepartments = async () => {
            let result = await getDepartments();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionDepartments = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
						type: elem.type,
					} 
				));
            }
		}

        const getOptionTables = async () => {
			let result = await getTables();
			
			state.selectOptionTables = result.map((elem: any) => (
				{
					value: elem.table_name,
					label: elem.description,
				} 
			));
		}

        const changeOptionTable = async (table: any) => {
            let result = await getColumns(table.value);
            
			state.selectOptionTableColumns = result.map((elem: any) => (
				{
					value: elem.column_name,
					label: elem.description,
				} 
			));
		}

		const changeCondition = async (index: number) => {
			if (!!state.dataSetupProcess[index].condition) {
				state.dataSetupProcess[index].condition_status = 'true';
			} else {
				state.dataSetupProcess[index].condition_status = '';
			}
		}

		const createForm = (): void => {
            state.showModalForm = true;
        }

		const closeModalForm = (): void => {
            state.showModalForm = false;
        }
		
		const closeWorkflow = async () => {
            await router.push('/workflow');
        }

		const createAction = (): void => {
            state.showModalAction = true;
			state.showComponentModal = true;
        }

		const createStage = (): void => {
            state.showModalStage = true;
			state.showComponentModal = true;
        }

		const createEmailTemplate = (): void => {
            state.showModalEmailTemplate = true;
			state.showComponentModal = true;
        }

		const createCondition = (): void => {
            state.showModalCondition = true;
			state.showComponentModal = true;
        }

		const createSyncGroup = (): void => {
            state.showModalSyncGroup = true;
			state.showComponentModal = true;
        }

		const createProcessTransition = (): void => {
            state.showModalProcessTransition = true;
			state.showComponentModal = true;
        }

		const createProcessGroup = (): void => {
			state.showModalProcessGroup = true;
			state.showComponentModal = true;
        }

		const hideModalProcessGroup = (): void => {
            state.showModalProcessGroup = false;    
        }

		const resetModalProcessGroup = (): void => {
			state.dataProcessGroup = {
				name: '',
			};
        }

		const hideModalAction = (): void => {
            state.showModalAction = false;    
        }

		const resetModalAction = (): void => {
			state.dataAction = {
				name: '',
				slug: '',
				description: '',
			};
        }

		const hideModalStage = (): void => {
            state.showModalStage = false;    
        }

		const resetModalStage = (): void => {
			state.dataStage = {
				name: '',
				slug: '',
				description: '',
				approver_ids: [],
				follower_ids: [],
				comment: '',
			};
        }

		const hideModalCondition = (): void => {
            state.showModalCondition = false;    
        }

		const resetModalCondition = (): void => {
			state.dataCondition = {
				name: '',
				slug: '',
				orConditions: [],
				arrayConditions: [] as Array<any>,
				arrayConditionSubs: [] as Array<any>,
			};
        }

		const hideModalEmailTemplate = (): void => {
            state.showModalEmailTemplate = false;    
        }

		const resetModalEmailTemplate = (): void => {
			state.dataEmailTemplate = {
				name: '',
				slug: '',
				condition: '',
				from_email: '',
				title_name: '',
				to_emails: [],
				cc_emails: [],
				bcc_emails: [],
				content: '',
				files: [],
			};
        }

		const hideModalSyncGroup = (): void => {
            state.showModalSyncGroup = false;    
        }

		const hideModalProcessTransition = (): void => {
            state.showModalProcessTransition = false;    
        }

		const resetModalSyncGroup = (): void => {
			state.dataSyncGroup = {
				name: '',
				slug: '',
			};
        }

		const resetModalProcessTransition = (): void => {
			state.dataProcessTransition = {
				name: '',
				slug: '',
				apply_process: '',
				type_create: 'auto',
			};
        }

		const addStageProcess = (): void => {
			if (state.dataSetupProcess.length == 0) {
				state.dataSetupProcess.push({
					from_stage: { from_stage: WORKFLOWS.STAGE.START },
					action_next: { action_next: WORKFLOWS.ACTION.CREATE },
					to_stage: '',
					condition: '',
					condition_status: '',
					email_template: '',
					sync_group: '',
					process_transition: '',
					back_to_stage: '',
					action_back_to: '',
					email_template_back: '',
				});
			} else {
				state.dataSetupProcess.push({
					from_stage: '',
					action_next: '',
					to_stage: '',
					condition: '',
					condition_status: '',
					email_template: '',
					sync_group: '',
					process_transition: '',
					back_to_stage: '',
					action_back_to: '',
					email_template_back: '',
				});
			}
		}

		const removeStageProcess = (index: number): void => {
			state.dataSetupProcess.splice(index, 1);
		}
		const updateAddSelectOptionActions = (optionAction: { name: string }): void => {
			const newOptionAction = {
				label: optionAction.name,
				value: optionAction
			};
			state.listDataActions.push(newOptionAction);
			state.selectOptionActionNexts = [...state.selectOptionActionNextDefaults, ...state.listDataActions];
		}

		const updateEditSelectOptionActions = (optionAction: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionAction = {
				label: optionAction.name,
				value: optionAction
			};
			state.listDataActions.splice(indexEdit, 1, newOptionAction);
			state.selectOptionActionNexts = [...state.selectOptionActionNextDefaults, ...state.listDataActions];
			updateDataSetupProcess(slugValue, state.stringActionNext);
		}

		const updateRemoveSelectOptionActions = (listDataActions: any, slugValue: string): void => {
			state.selectOptionActionNexts = [...state.selectOptionActionNextDefaults, ...listDataActions];
			updateDataSetupProcess(slugValue, state.stringActionNext);
		}

		const updateAddSelectOptionStages = (optionStage: { name: string }): void => {
			const newOptionStage = {
				label: optionStage.name,
				value: optionStage
			};
			// Kiểm tra cho tôi xem có tên nào trùng trong state.listDataStages không
			const isExist = state.listDataStages.some((stage: any) => stage.label === optionStage.name);
            if (!isExist) {
                state.listDataStages.push(newOptionStage);
				state.selectOptionFromStages = [...state.selectOptionFromStageDefaults, ...state.listDataStages];
				state.selectOptionToStages = [...state.selectOptionToStageDefaults, ...state.listDataStages];
				updateOptionTypeSelectInitialDataFields(state.selectOptionFromStages);
            }
		}

		const updateOptionTypeSelectInitialDataFields = (selectOptionFromStages: any): void => {
			// Tìm object có type là 'select'
			const selectField = initialDataFields.find(field => field.type === 'select');
			if (selectField) {
				selectField.options = selectOptionFromStages.map((option: any) => ({
					label: option.label,
					value: option.value,
				}));
			}
		}

		const updateEditSelectOptionStages = (optionStage: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionStage = {
				label: optionStage.name,
				value: optionStage
			};
			// Kiểm tra cho tôi xem có tên nào trùng trong state.listDataStages không loại trừ tên đang được sửa
			const isExistExcludeCurrent = state.listDataStages.some((stage: any, indexStage: number) => indexStage !== indexEdit && stage.label === optionStage.name);
			if (!isExistExcludeCurrent) {
				state.listDataStages.splice(indexEdit, 1, newOptionStage);
				state.selectOptionFromStages = [...state.selectOptionFromStageDefaults, ...state.listDataStages];
				state.selectOptionToStages = [...state.selectOptionToStageDefaults, ...state.listDataStages];
				updateOptionTypeSelectInitialDataFields(state.selectOptionFromStages);
				updateDataSetupProcess(slugValue, state.stringStage);
			}
		}

		const updateRemoveSelectOptionStages = (listDataStages: any, slugValue: string): void => {
			state.selectOptionFromStages = [...state.selectOptionFromStageDefaults, ...listDataStages];
			state.selectOptionToStages = [...state.selectOptionToStageDefaults, ...state.listDataStages];
			updateOptionTypeSelectInitialDataFields(state.selectOptionFromStages);
			updateDataSetupProcess(slugValue, state.stringStage);
		}

		const updateAddSelectOptionEmailTemplates = (optionEmailTemplate: { name: string }): void => {
			const newOptionEmailTemplate = {
				label: optionEmailTemplate.name,
				value: optionEmailTemplate
			};
			state.listDataEmailTemplates.push(newOptionEmailTemplate);
			state.selectOptionEmailTemplates = [...state.selectOptionEmailTemplateDefaults, ...state.listDataEmailTemplates];
		}

		const updateEditSelectOptionEmailTemplates = (optionEmailTemplate: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionEmailTemplate = {
				label: optionEmailTemplate.name,
				value: optionEmailTemplate
			};
			state.listDataEmailTemplates.splice(indexEdit, 1, newOptionEmailTemplate);
			state.selectOptionEmailTemplates = [...state.selectOptionEmailTemplateDefaults, ...state.listDataEmailTemplates];
			updateDataSetupProcess(slugValue, state.stringEmailTemplate);
		}

		const updateRemoveSelectOptionEmailTemplates = (listDataEmailTemplates: any, slugValue: string): void => {
			state.selectOptionEmailTemplates = [...state.selectOptionEmailTemplateDefaults, ...listDataEmailTemplates];
			updateDataSetupProcess(slugValue, state.stringEmailTemplate);
		}

		const updateAddSelectOptionConditions = (optionCondition: { name: string }): void => {
			const newOptionCondition = {
				label: optionCondition.name,
				value: optionCondition
			};
			state.listDataConditions.push(newOptionCondition);
			state.selectOptionConditions = [...state.selectOptionConditionDefaults, ...state.listDataConditions];
		}

		const updateEditSelectOptionConditions = (optionCondition: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionCondition = {
				label: optionCondition.name,
				value: optionCondition
			};
			state.listDataConditions.splice(indexEdit, 1, newOptionCondition);
			state.selectOptionConditions = [...state.selectOptionConditionDefaults, ...state.listDataConditions];
			updateDataSetupProcess(slugValue, state.stringCondition);
		}

		const updateRemoveSelectOptionConditions = (listDataConditions: any, slugValue: string): void => {
			state.selectOptionConditions = [...state.selectOptionConditionDefaults, ...listDataConditions];
			updateDataSetupProcess(slugValue, state.stringCondition);
		}
		const updateAddSelectOptionSyncGroups = (optionSyncGroup: { name: string }): void => {
			const newOptionSyncGroup = {
				label: optionSyncGroup.name,
				value: optionSyncGroup
			};
			state.listDataSyncGroups.push(newOptionSyncGroup);
			state.selectOptionSyncGroups = [...state.selectOptionSyncGroupDefaults, ...state.listDataSyncGroups];
		}

		const updateEditSelectOptionSyncGroups = (optionSyncGroup: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionSyncGroup = {
				label: optionSyncGroup.name,
				value: optionSyncGroup
			};
			state.listDataSyncGroups.splice(indexEdit, 1, newOptionSyncGroup);
			state.selectOptionSyncGroups = [...state.selectOptionSyncGroupDefaults, ...state.listDataSyncGroups];
			updateDataSetupProcess(slugValue, state.stringSyncGroup);
		}

		const updateRemoveSelectOptionSyncGroups = (listDataSyncGroups: any, slugValue: string): void => {
			state.selectOptionSyncGroups = [...state.selectOptionSyncGroupDefaults, ...listDataSyncGroups];
			updateDataSetupProcess(slugValue, state.stringSyncGroup);
		}

		const updateAddSelectOptionProcessTransitions = (optionProcessTransition: { name: string }): void => {
			const newOptionProcessTransition = {
				label: optionProcessTransition.name,
				value: optionProcessTransition
			};
			state.listDataProcessTransitions.push(newOptionProcessTransition);
			state.selectOptionProcessTransitions = [...state.selectOptionProcessTransitionDefaults, ...state.listDataProcessTransitions];
		}

		const updateEditSelectOptionProcessTransitions = (optionProcessTransition: { name: string }, indexEdit: number, slugValue: string): void => {
			const newOptionProcessTransition = {
				label: optionProcessTransition.name,
				value: optionProcessTransition
			};
			state.listDataProcessTransitions.splice(indexEdit, 1, newOptionProcessTransition);
			state.selectOptionProcessTransitions = [...state.selectOptionProcessTransitionDefaults, ...state.listDataProcessTransitions];
			updateDataSetupProcess(slugValue, state.stringProcessTransition);
		}

		const updateRemoveSelectOptionProcessTransitions = (listDataProcessTransitions: any, slugValue: string): void => {
			state.selectOptionProcessTransitions = [...state.selectOptionProcessTransitionDefaults, ...listDataProcessTransitions];
			updateDataSetupProcess(slugValue, state.stringProcessTransition);
		}

		const updateDataSetupProcess = (slugValue: string, typeUpdate: string) => {
			// Kiểm tra từng phần tử trong `state.dataSetupProcess`
			state.dataSetupProcess.forEach((setupProcess: any) => {
				//Viết cho tôi swich theo các trườn hợp check typeUpdate
				switch (typeUpdate) {
					case state.stringActionNext:
						if (setupProcess.action_next && setupProcess.action_next.slug === slugValue) {
							setupProcess.action_next = '';
						}
						break;
					case state.stringStage:
						if (setupProcess.from_stage && setupProcess.from_stage.slug === slugValue) {
							setupProcess.from_stage = '';
						}
						if (setupProcess.to_stage && setupProcess.to_stage.slug === slugValue) {
							setupProcess.to_stage = '';
						}
						if (setupProcess.back_to_stage && setupProcess.back_to_stage.slug === slugValue) {
							setupProcess.back_to_stage = '';
						}
						break;
					case state.stringCondition:
						if (setupProcess.condition && setupProcess.condition.slug === slugValue) {
							setupProcess.condition = '';
							setupProcess.condition_status = '';
						}
						break;
					case state.stringSyncGroup:
						if (setupProcess.sync_group && setupProcess.sync_group.slug === slugValue) {
							setupProcess.sync_group = '';
						}
						break;
					case state.stringEmailTemplate:
						if (setupProcess.email_template && setupProcess.email_template.slug === slugValue) {
							setupProcess.email_template = '';
							setupProcess.email_template_back = '';
						}
						break;
					case state.stringProcessTransition:
						if (setupProcess.process_transition && setupProcess.process_transition.slug === slugValue) {
							setupProcess.process_transition = '';
						}
						break;
				}
			});
		}
		
		return {
			state,
            schema,
			setIsLoading,
			contextMenuOptions,
			isContextMenuVisible,
			contextMenuPosition,
			showContextMenu,
			handleContextMenu,
			startPress,
			endPress,
			handleOption,
			debouncedGetOptionUsers,
			debouncedGetOptionProcessGroups,
			debouncedGetOptionScopes,
			dynamicForm,
			setSubmitStatus,
			submitForm,
			handleSubmitForm,
			handleSubmitWorkflow,
			handleSubmitField,
			closeField,
			selectTypeField,
			snakeCaseDisplayName,
			handleInputDisplayName,
			addItemSelectOptions,
			removeSelectOptions,
			selectOptionChildrens,
            changeOptionTable,
			changeCondition,
			getFieldCreated,
			hiddenContextMenuVisible,
			createForm,
			createAction,
			createStage,
			createEmailTemplate,
			createProcessTransition,
			createCondition,
			createSyncGroup,
			closeModalForm,
			closeWorkflow,
			createProcessGroup,
			hideModalProcessGroup,
			resetModalProcessGroup,
			hideModalAction,
			resetModalAction,
			hideModalStage,
			resetModalStage,
			hideModalCondition,
			resetModalCondition,
			hideModalEmailTemplate,
			resetModalEmailTemplate,
			hideModalSyncGroup,
			hideModalProcessTransition,
			resetModalSyncGroup,
			resetModalProcessTransition,
			addStageProcess,
			removeStageProcess,
			updateAddSelectOptionActions,
			updateEditSelectOptionActions,
			updateRemoveSelectOptionActions,
			updateAddSelectOptionStages,
			updateEditSelectOptionStages,
			updateRemoveSelectOptionStages,
			updateAddSelectOptionEmailTemplates,
			updateEditSelectOptionEmailTemplates,
			updateRemoveSelectOptionEmailTemplates,
			updateAddSelectOptionConditions,
			updateEditSelectOptionConditions,
			updateRemoveSelectOptionConditions,
			updateAddSelectOptionSyncGroups,
			updateEditSelectOptionSyncGroups,
			updateRemoveSelectOptionSyncGroups,
			updateAddSelectOptionProcessTransitions,
			updateEditSelectOptionProcessTransitions,
			updateRemoveSelectOptionProcessTransitions,
		}
    }
});
</script>

<style scoped>
	.cursor-pointer {
		cursor: pointer;
	}
	.column-width-td {
		min-width: 200px !important;
	}
	.card-footer {
		z-index: 99;
		position: sticky;
		left: 0px;
		bottom: 0px;
		width: 100%;
		background-color:#f8f9fa;
		padding: 10px;
	}
    .list-group-item:hover {
        cursor: pointer;
        border: 2px dashed #5c636a;
    }
	.table__td--stage {
		min-width: 200px !important;
	}
	.formkit-outer {
		margin-top: 10px !important;
	}
	.feature-card {
		background-color: #f8f8f8;
		border-radius: 8px;
		padding: 15px;
		border: 1px solid #e9e9e9;
		cursor: pointer;
		display: flex; /* Sử dụng Flexbox để căn chỉnh */
		align-items: center; /* Căn giữa theo chiều dọc */
		gap: 16px; /* Khoảng cách giữa icon và nội dung */
	}
	.feature-card:hover {
		background-color: rgb(0 0 0 / 8%);
	}
	
	.feature-title {
		color: #052c65;
		font-weight: bold;
		margin-bottom: 5px;
	}
	
	.feature-desc {
		color: #666;
		font-size: 14px;
		margin-bottom: 0;
	}
	.form-select {
		line-height: 1.875;
	}
	.icon-info {
		font-size: 16px !important;
		color: #0d6efd !important;
	}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>